{"tasks": [{"id": "abcd24bc-7ecb-4d5f-ab2b-c28617cbcba0", "name": "Fix Dramatiq retry state detection in retry_tracker.py", "description": "Modify the should_send_notification method in DramatiqRetryTracker class to use Dramatiq's authoritative retry state as primary source instead of custom counting. This fixes the core synchronization bug where custom tracker shows 2/3 retries while Dramatiq correctly reaches 3/3.", "notes": "Critical: This is the root cause fix. Must maintain existing API for other callers. Add enhanced logging to show retry source (dramatiq_definitive vs fallback_custom).", "status": "pending", "dependencies": [], "createdAt": "2025-08-07T16:22:45.488Z", "updatedAt": "2025-08-07T16:22:45.488Z", "relatedFiles": [{"path": "src/tasks/retry_tracker.py", "type": "TO_MODIFY", "description": "DramatiqRetryTracker class, should_send_notification method", "lineStart": 113, "lineEnd": 135}, {"path": "dramatiq_config.py", "type": "REFERENCE", "description": "Dramatiq Retries middleware configuration", "lineStart": 54, "lineEnd": 61}], "implementationGuide": "In src/tasks/retry_tracker.py, update should_send_notification() method:\\n\\n```python\\ndef should_send_notification(self, task_id: str, max_retries: int = 3) -> bool:\\n    # Primary: Use Dramatiq's authoritative state\\n    try:\\n        current_message = dramatiq.get_current_message()\\n        if current_message:\\n            # Check if <PERSON><PERSON><PERSON> has determined no more retries\\n            if hasattr(current_message, 'failed') and current_message.failed:\\n                return len(current_message.failed) >= max_retries\\n            # Check options for retry count\\n            if 'retries' in current_message.options:\\n                retries_left = current_message.options['retries']\\n                return retries_left <= 0\\n    except Exception as e:\\n        logger.warning(f'Failed to get Dramatiq retry state: {e}')\\n    \\n    # Fallback: Use existing custom tracker logic\\n    return self._custom_retry_decision(task_id, max_retries)\\n```\\n\\nAdd _custom_retry_decision() method to encapsulate existing logic. Preserve all existing public methods for backwards compatibility.", "verificationCriteria": "Test shows Dramatiq message with exhausted retries correctly returns True for should_send_notification. Test with partial retries returns False. Fallback logic works when Dramatiq message unavailable.", "analysisResult": "Fix critical retry counting synchronization bug between Dramatiq and custom retry tracker. The issue causes LINE notifications to never be sent when tasks fail after reaching maximum retries. Solution uses Dramatiq's authoritative retry state as single source of truth while maintaining backwards compatibility with existing architecture."}, {"id": "00c79264-f6f6-41fe-b3a3-fb939da17b58", "name": "Integrate with existing VendorFileMonitor retry tracking", "description": "Sync the custom retry tracker with VendorFileMonitor's retry_count field to maintain consistency across monitoring systems. This prevents duplicate tracking and ensures dashboard accuracy.", "notes": "Maintains consistency between retry_tracker and vendor_file_monitor systems. Non-critical if VendorFileMonitor unavailable.", "status": "pending", "dependencies": [{"taskId": "abcd24bc-7ecb-4d5f-ab2b-c28617cbcba0"}], "createdAt": "2025-08-07T16:22:45.488Z", "updatedAt": "2025-08-07T16:22:45.488Z", "relatedFiles": [{"path": "src/tasks/retry_tracker.py", "type": "TO_MODIFY", "description": "track_task_attempt and increment_retry_count methods", "lineStart": 68, "lineEnd": 160}, {"path": "src/services/vendor_file_monitor.py", "type": "REFERENCE", "description": "VendorFileMetrics retry_count field", "lineStart": 55, "lineEnd": 58}], "implementationGuide": "In src/tasks/retry_tracker.py, modify track_task_attempt and increment_retry_count methods:\\n\\n```python\\ndef track_task_attempt(self, task_id: str, vendor_code: str, mo: str, attempt_number: int = 0, is_retry: bool = False):\\n    # Existing storage logic\\n    retry_info = {...}  # existing code\\n    self.storage.store_retry_info(task_id, retry_info)\\n    \\n    # NEW: Sync with VendorFileMonitor if tracking_id available\\n    try:\\n        from src.services.vendor_file_monitor import get_vendor_file_monitor\\n        monitor = get_vendor_file_monitor()\\n        # Update monitor retry count to match our tracking\\n        monitor.record_retry(task_id, f'Retry {attempt_number}: {vendor_code}/{mo}')\\n    except Exception as e:\\n        logger.debug(f'VendorFileMonitor sync failed: {e}')\\n```\\n\\nEnsure retry counts stay synchronized between systems for accurate dashboard display.", "verificationCriteria": "Dashboard shows consistent retry counts between retry tracker and vendor file monitor. No duplicate counting occurs. System gracefully handles VendorFileMonitor unavailability.", "analysisResult": "Fix critical retry counting synchronization bug between Dramatiq and custom retry tracker. The issue causes LINE notifications to never be sent when tasks fail after reaching maximum retries. Solution uses Dramatiq's authoritative retry state as single source of truth while maintaining backwards compatibility with existing architecture."}, {"id": "c9829e5e-6156-47eb-8c02-27ce31570902", "name": "Enhance error handling and logging in pipeline_tasks.py", "description": "Update the error handling logic in process_vendor_files_task to use the fixed retry detection and provide clearer logging about retry decisions. Remove redundant retry counting logic that conflicts with Dramatiq.", "notes": "Removes conflicting retry logic and delegates authority to Dramatiq + fixed retry_tracker. Improves log clarity for debugging.", "status": "pending", "dependencies": [{"taskId": "abcd24bc-7ecb-4d5f-ab2b-c28617cbcba0"}], "createdAt": "2025-08-07T16:22:45.488Z", "updatedAt": "2025-08-07T16:22:45.488Z", "relatedFiles": [{"path": "src/tasks/pipeline_tasks.py", "type": "TO_MODIFY", "description": "Exception handling in process_vendor_files_task", "lineStart": 396, "lineEnd": 503}, {"path": "src/tasks/retry_tracker.py", "type": "DEPENDENCY", "description": "Fixed should_send_notification method", "lineStart": 113, "lineEnd": 135}], "implementationGuide": "In src/tasks/pipeline_tasks.py, update the exception handling in process_vendor_files_task:\\n\\n```python\\n# Around lines 403-477, replace existing retry logic with:\\ndef handle_task_failure(e: Exception, task_id: str, vendor_code: str, mo: str, ...):\\n    # Remove redundant custom retry detection\\n    # Use fixed retry_tracker for authoritative decision\\n    retry_tracker = get_retry_tracker()\\n    \\n    # This now uses Dramatiq's authoritative state\\n    should_notify = retry_tracker.should_send_notification(task_id, max_retries=3)\\n    \\n    logger.info(f'[PIPELINE] Retry decision: task={task_id}, will_notify={should_notify}, source=dramatiq_authoritative')\\n    \\n    if should_notify:\\n        send_failure_notification_if_needed(...)\\n        logger.info(f'[PIPELINE] Final failure notification sent: {vendor_code}/{mo}')\\n    else:\\n        logger.info(f'[PIPELINE] Task will retry, notification deferred: {vendor_code}/{mo}')\\n    \\n    # Let <PERSON><PERSON><PERSON> handle the retry logic\\n    raise  # Always re-raise to let <PERSON><PERSON><PERSON> decide retry\\n```", "verificationCriteria": "Logs show clear retry decisions with source attribution. No duplicate retry counting logic. Failed tasks correctly trigger notifications after Dramatiq exhausts retries.", "analysisResult": "Fix critical retry counting synchronization bug between Dramatiq and custom retry tracker. The issue causes LINE notifications to never be sent when tasks fail after reaching maximum retries. Solution uses Dramatiq's authoritative retry state as single source of truth while maintaining backwards compatibility with existing architecture."}, {"id": "90cc509f-36cc-4a52-8625-ca8226cb46d3", "name": "Add retry state validation and monitoring", "description": "Create validation mechanisms to detect retry counting mismatches and add monitoring to catch similar issues early. Include startup validation and runtime consistency checks.", "notes": "Preventive measure to catch similar issues early. Non-blocking validation that logs inconsistencies without affecting operation.", "status": "pending", "dependencies": [{"taskId": "abcd24bc-7ecb-4d5f-ab2b-c28617cbcba0"}], "createdAt": "2025-08-07T16:22:45.488Z", "updatedAt": "2025-08-07T16:22:45.488Z", "relatedFiles": [{"path": "src/tasks/retry_tracker.py", "type": "TO_MODIFY", "description": "Add validation and monitoring functions", "lineStart": 300, "lineEnd": 312}], "implementationGuide": "Create new validation functions in retry_tracker.py:\\n\\n```python\\ndef validate_retry_consistency(task_id: str) -> Dict[str, Any]:\\n    '''Validate consistency between Dramatiq and custom retry state'''\\n    dramatiq_state = _get_dramatiq_retry_state(task_id)\\n    custom_state = self.get_retry_count(task_id)\\n    \\n    return {\\n        'task_id': task_id,\\n        'dramatiq_retries': dramatiq_state.get('retries', 0),\\n        'custom_retries': custom_state[0],\\n        'consistent': abs(dramatiq_state.get('retries', 0) - custom_state[0]) <= 1,\\n        'timestamp': datetime.now().isoformat()\\n    }\\n\\ndef setup_retry_monitoring():\\n    '''Setup periodic consistency checks'''\\n    # Add monitoring hook to detect discrepancies\\n    logger.info('Retry state monitoring enabled')\\n```\\n\\nAdd startup validation in __init__ to verify Dramatiq configuration matches expectations.", "verificationCriteria": "Validation detects retry count mismatches when they occur. Monitoring logs help identify issues early. System continues normal operation even if validation fails.", "analysisResult": "Fix critical retry counting synchronization bug between Dramatiq and custom retry tracker. The issue causes LINE notifications to never be sent when tasks fail after reaching maximum retries. Solution uses Dramatiq's authoritative retry state as single source of truth while maintaining backwards compatibility with existing architecture."}, {"id": "0af7b3b0-b75f-4d8d-a1bf-634e3099597a", "name": "Update debug scripts and add comprehensive test", "description": "Update existing debug scripts to test the fix and create comprehensive test to verify retry counting synchronization works correctly under various failure scenarios.", "notes": "Essential for verifying the fix works correctly. Tests both Dramatiq state detection and fallback logic.", "status": "pending", "dependencies": [{"taskId": "abcd24bc-7ecb-4d5f-ab2b-c28617cbcba0"}, {"taskId": "c9829e5e-6156-47eb-8c02-27ce31570902"}], "createdAt": "2025-08-07T16:22:45.488Z", "updatedAt": "2025-08-07T16:22:45.488Z", "relatedFiles": [{"path": "debug_line_notification_issue.py", "type": "TO_MODIFY", "description": "Add retry synchronization test", "lineStart": 140, "lineEnd": 180}, {"path": "test_retry_synchronization_fix.py", "type": "CREATE", "description": "New comprehensive test file for retry bug fix"}], "implementationGuide": "Update debug_line_notification_issue.py to test the fix:\\n\\n```python\\ndef test_retry_synchronization():\\n    '''Test retry counting synchronization fix'''\\n    print('=== Retry Synchronization Test ===')\\n    \\n    # Test 1: Simulate Dramatiq message with exhausted retries\\n    mock_message = MockDramatiqMessage(retries=3, failed=['error1', 'error2', 'error3'])\\n    with mock.patch('dramatiq.get_current_message', return_value=mock_message):\\n        tracker = get_retry_tracker()\\n        should_notify = tracker.should_send_notification('test_task', max_retries=3)\\n        assert should_notify == True, 'Should notify when retries exhausted'\\n    \\n    # Test 2: Simulate partial retries\\n    mock_message = MockDramatiqMessage(retries=1, failed=['error1'])\\n    with mock.patch('dramatiq.get_current_message', return_value=mock_message):\\n        should_notify = tracker.should_send_notification('test_task', max_retries=3)\\n        assert should_notify == False, 'Should not notify when retries available'\\n    \\n    print('[OK] Retry synchronization tests passed')\\n```\\n\\nCreate MockDramatiqMessage class to simulate various retry states for testing.", "verificationCriteria": "Debug script shows retry counting synchronization working correctly. Tests pass for various retry states. Mock Dramatiq messages correctly simulate real behavior.", "analysisResult": "Fix critical retry counting synchronization bug between Dramatiq and custom retry tracker. The issue causes LINE notifications to never be sent when tasks fail after reaching maximum retries. Solution uses Dramatiq's authoritative retry state as single source of truth while maintaining backwards compatibility with existing architecture."}]}