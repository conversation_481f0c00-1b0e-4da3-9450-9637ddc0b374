"""
LINE 通知問題調試腳本
測試為什麼 LINE 通知沒有在重試失敗後發送

🎯 測試場景：
1. 模擬任務重試失敗的情況
2. 檢查 notification_available 狀態
3. 驗證 LINE_USER_ID 環境變數問題
4. 測試完整的通知發送流程
"""

import os
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from src.tasks.retry_tracker import DramatiqRetryTracker, send_failure_notification_if_needed
from src.services.vendor_file_notification import VendorFileNotificationService, get_vendor_file_notification_service
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService

def debug_environment_variables():
    """檢查環境變數配置"""
    print("=== 環境變數檢查 ===")
    print(f"LINE_CHANNEL_ACCESS_TOKEN: {'設定' if os.getenv('LINE_CHANNEL_ACCESS_TOKEN') else '未設定'}")
    print(f"LINE_USER_ID: {'設定' if os.getenv('LINE_USER_ID') else '[ERROR] 未設定'}")
    print(f"LINE_NOTIFY_PARSING_FAILURE: {os.getenv('LINE_NOTIFY_PARSING_FAILURE', 'default:true')}")
    print(f"LINE_NOTIFY_VENDOR_FILE_FAILURE: {os.getenv('LINE_NOTIFY_VENDOR_FILE_FAILURE', 'default:true')}")
    print()

def debug_line_notification_service():
    """測試 LINE 通知服務初始化"""
    print("=== LINE 通知服務測試 ===")
    try:
        line_service = LineNotificationService()
        print("[OK] LINE 通知服務初始化成功")
        print(f"   - 失敗通知啟用: {line_service.notify_parsing_failure}")
        print(f"   - 成功通知啟用: {line_service.notify_parsing_success}")
        return line_service
    except Exception as e:
        print(f"[ERROR] LINE 通知服務初始化失敗: {e}")
        return None

def debug_vendor_notification_service():
    """測試廠商通知服務"""
    print("=== 廠商通知服務測試 ===")
    try:
        vendor_service = VendorFileNotificationService()
        print("[OK] 廠商通知服務初始化成功")
        print(f"   - 通知啟用: {vendor_service.notification_enabled}")
        print(f"   - 基礎路徑: {vendor_service.base_path}")
        return vendor_service
    except Exception as e:
        print(f"[ERROR] 廠商通知服務初始化失敗: {e}")
        return None

def debug_retry_tracker():
    """測試重試追蹤器初始化"""
    print("=== 重試追蹤器測試 ===")
    try:
        tracker = DramatiqRetryTracker()
        print("[OK] 重試追蹤器初始化成功")
        print(f"   - 存儲類型: {tracker.storage_type}")
        print(f"   - 通知可用: {tracker.notification_available}")
        
        # 檢查通知服務
        if tracker.notification_service:
            print("   - 通知服務: 已初始化")
        else:
            print("   - 通知服務: [ERROR] 初始化失敗")
        
        return tracker
    except Exception as e:
        print(f"[ERROR] 重試追蹤器初始化失敗: {e}")
        return None

def test_notification_flow():
    """測試完整的通知發送流程"""
    print("=== 通知發送流程測試 ===")
    
    # 模擬任務失敗場景
    test_params = {
        'task_id': 'test_task_debug_123',
        'vendor_code': 'GTK',
        'mo': 'DEBUG_MO_123',
        'temp_path': '/test/path/file.csv',
        'pd': 'TEST_PD',
        'lot': 'TEST_LOT',
        'error_message': 'Debug test: 模擬任務重試失敗',
        'exception': Exception("模擬異常：檔案處理失敗"),
        'email_subject': 'Debug Test Email',
        'email_body': 'This is a debug test',
        'tracking_id': 'debug_tracking_123',
        'processing_time': 45.5
    }
    
    try:
        # 呼叫完整的通知發送流程
        notification_sent = send_failure_notification_if_needed(**test_params)
        
        if notification_sent:
            print("[OK] 通知發送流程完成")
        else:
            print("[ERROR] 通知發送流程失敗或被跳過")
        
        return notification_sent
    except Exception as e:
        print(f"[ERROR] 通知發送流程異常: {e}")
        return False

def test_direct_notification():
    """直接測試廠商通知服務"""
    print("=== 直接通知測試 ===")
    
    try:
        vendor_service = get_vendor_file_notification_service()
        
        success = vendor_service.notify_vendor_file_processing_failure(
            vendor_code='GTK',
            mo='DIRECT_TEST_MO',
            temp_path='/test/direct/path.csv',
            pd='DIRECT_PD',
            lot='DIRECT_LOT',
            error_message='Direct test notification',
            email_subject='Direct Test Subject',
            email_body='Direct test body',
            task_id='direct_test_task',
            tracking_id='direct_test_tracking',
            processing_time=30.0,
            retry_count=3
        )
        
        if success:
            print("[OK] 直接通知發送成功")
        else:
            print("[ERROR] 直接通知發送失敗")
            
        return success
    except Exception as e:
        print(f"[ERROR] 直接通知測試異常: {e}")
        return False

def main():
    """主要調試流程"""
    print("[DEBUG] LINE 通知問題調試開始")
    print("=" * 60)
    
    # 步驟1: 檢查環境變數
    debug_environment_variables()
    
    # 步驟2: 測試各個服務初始化
    line_service = debug_line_notification_service()
    vendor_service = debug_vendor_notification_service() 
    retry_tracker = debug_retry_tracker()
    
    print()
    
    # 步驟3: 測試通知發送流程
    if retry_tracker and retry_tracker.notification_available:
        print("[TEST] 開始測試通知發送...")
        test_notification_flow()
        print()
        
        print("[TEST] 開始直接通知測試...")
        test_direct_notification()
    else:
        print("[WARN] 通知服務不可用，無法測試發送流程")
        
        # 嘗試找出具體原因
        if not os.getenv('LINE_USER_ID'):
            print("[REASON] 可能原因：LINE_USER_ID 環境變數未設定")
        if not os.getenv('LINE_CHANNEL_ACCESS_TOKEN'):
            print("[REASON] 可能原因：LINE_CHANNEL_ACCESS_TOKEN 環境變數未設定")
    
    print()
    print("=" * 60)
    print("[DEBUG] LINE 通知問題調試完成")

if __name__ == "__main__":
    main()