# Outlook Summary System 全端架構文件

## 1. 介紹 (Introduction)

這份文件概述了 **Outlook Summary System** 的完整全端架構，包含後端系統、前端實作以及它們之間的整合。它將作為 AI 驅動開發的唯一真實來源，確保整個技術堆疊的一致性。

這種統一的方法結合了傳統上獨立的後端和前端架構文件，為現代全端應用程式開發流程提供了簡化，因為在現代應用中，前後端的關注點日益交織。

#### **Rationale (決策理由)**

我將這份文件定位為一份統一的全端架構，而不是將前端和後端分開。這是因為 `project_info.json` 顯示這是一個包含 500 多個 Python 檔案和 Playwright 前端測試的複雜系統。這表明前後端緊密耦合，分開討論架構可能會忽略重要的整合點。將它們合併到一份文件中，可以更清晰地呈現系統全貌，有助於 AI 開發代理理解完整的開發背景。

### 1.1. 啟動模板或現有專案 (Starter Template or Existing Project)

這是一個現有的「棕地」專案。所有新的開發都必須基於現有的程式碼庫和架構。

#### **Rationale (決策理由)**

從 `project_info.json` 和檔案列表中可以清楚地看到，這是一個龐大且成熟的專案，擁有超過 23,000 個檔案和 175 次 git 提交。因此，不存在使用新模板的選項，所有工作都必須在現有專案的基礎上進行。

## 2. 高階架構 (High Level Architecture)

#### **技術摘要 (Technical Summary)**

Outlook Summary System 採用了基於 Python 的微服務架構，並透過 Docker 進行容器化部署。系統核心由兩個主要服務構成：一個使用 **Flask** 框架的郵件收件夾服務，負責郵件的接收、管理與展示；以及一個使用 **FastAPI** 的高效能 API 服務，專門處理複雜的 EQC (Engineering Quality Control) 業務邏輯。為了處理耗時的計算任務，系統整合了 **Dramatiq** 任務隊列與 **Redis**，實現了強大的非同步處理能力。前端介面主要由 Flask 渲染的 HTML 搭配 JavaScript (jQuery/AJAX) 構成，而 FastAPI 也提供了獨立的 UI 介面。整個系統的監控是透過 **Prometheus** 和 **Grafana** 來實現的。

#### **平台與基礎設施選擇 (Platform and Infrastructure Choice)**

*   **平台:** Docker
*   **核心服務:** 
    *   `outlook-summary`: 主應用程式容器，包含 Flask 和 FastAPI 服務。
    *   `redis`: 用於 Dramatiq 任務隊列和可能的快取。
    *   `prometheus`: 用於收集系統指標。
    *   `grafana`: 用於視覺化監控儀表板。
    *   `nginx`: (可選) 作為反向代理。
*   **部署主機與區域:** 本地部署 (透過 `docker-compose`)，但設計上具備移植到雲端平台 (如 AWS, GCP) 的潛力。

#### **儲存庫結構 (Repository Structure)**

*   **結構:** 單一儲存庫 (Monorepo)
*   **Monorepo 工具:** 無 (手動管理)
*   **套件組織:** 
    *   `src/`: 核心應用程式邏輯。
    *   `deployment/docker/`: Docker 相關的部署文件。
    *   `tests/`: 測試程式碼。
    *   `docs/`: 專案文件。
    *   `config/`: 設定檔。

#### **高階架構圖 (High Level Architecture Diagram)**

```mermaid
graph TD
    subgraph "使用者"
        A[使用者/外部系統]
    end

    subgraph "負載平衡/API 閘道 (Nginx - 可選)"
        B(Nginx)
    end

    subgraph "應用層 (Docker 容器)"
        C[Flask Web App (Port 5555)]
        D[FastAPI EQC Service (Port 8010)]
        E[Dramatiq Workers]
    end

    subgraph "資料與快取層 (Docker 容器)"
        F[PostgreSQL/SQLite]
        G[Redis]
    end

    subgraph "監控層 (Docker 容器)"
        H[Prometheus]
        I[Grafana]
    end

    A --> B
    B --> C
    B --> D
    C --> F
    C --> G
    D --> E
    E --> G
    E --> F
    H --> C
    H --> D
    I --> H
```

#### **架構模式 (Architectural Patterns)**

*   **微服務架構:** 系統被拆分為獨立的 Flask 和 FastAPI 服務，各自負責不同的業務領域。
*   **任務隊列模式:** 使用 Dramatiq 和 Redis 來非同步執行長時間運行的任務，提高了系統的回應性和可擴展性。
*   **容器化部署:** 使用 Docker 和 Docker Compose 來封裝和管理應用程式及其依賴，確保了開發和生產環境的一致性。
*   **反向代理:** (可選) 使用 Nginx 作為所有傳入流量的單一入口點，處理負載平衡、SSL 終止和靜態內容服務。

#### **Rationale (決策理由)**

我根據 `docker-compose.yml` 和 `requirements.txt` 的內容，推斷出這是一個多服務的微服務架構。將 Flask 和 FastAPI 分開，是典型的職責分離模式，Flask 處理使用者互動和介面，而 FastAPI 專注於高效能的 API 計算。Dramatiq 的使用是處理背景任務的關鍵，這對於需要時間處理的 EQC 分析至關重要。整個架構的設計顯示了對可擴展性、可維護性和可觀測性的考量。

## 3. 技術堆疊 (Tech Stack)

這份表格是整個專案的 **權威性技術選型**。所有開發工作都必須嚴格遵守此處列出的技術和版本。

| 分類 | 技術 | 版本 | 用途 | 決策理由 | 
| :--- | :--- | :--- | :--- | :--- |
| **前端語言** | JavaScript (ES6+) | | UI 互動與 API 呼叫 | 瀏覽器原生支援，生態系成熟。 |
| **前端框架** | (無特定框架) | | | 系統前端主要由後端 Flask 模板驅動，搭配 jQuery/AJAX。 |
| **UI 元件庫** | (未使用) | | | |
| **狀態管理** | (未使用) | | | |
| **後端語言** | Python | 3.11 | 核心業務邏輯與 API 開發 | 生態豐富，適合快速開發與資料處理。 |
| **後端框架** | Flask | 2.3.3 | 郵件收件夾 Web UI 與相關 API | 輕量級，適合建構傳統 Web 應用。 |
| | FastAPI | 0.104.1 | 高效能 EQC 處理 API | 非同步支援，性能優越，自動生成 API 文件。 |
| **API 風格** | REST | | 前後端與服務間通訊 | 成熟、普遍，易於理解和實現。 |
| **資料庫** | SQLite / PostgreSQL | | 郵件資料與應用狀態儲存 | SQLite 用於本地開發，PostgreSQL (可能) 用於生產。 |
| **快取** | Redis | 7-alpine | Dramatiq 任務隊列 Broker | 高效能的記憶體資料庫，適合任務隊列和快取。 |
| **檔案儲存** | 本地檔案系統 | | 附件與處理中檔案的儲存 | 簡單直接，透過 Docker Volume 掛載管理。 |
| **認證** | (未指定) | | | 系統目前似乎沒有明確的使用者認證機制。 |
| **前端測試** | Playwright | 1.40.0 | 端對端 (E2E) 測試 | 功能強大，支援多瀏覽器，適合複雜流程測試。 |
| **後端測試** | Pytest | | 單元與整合測試 | Python 社群最流行的測試框架。 |
| **E2E 測試** | Playwright | 1.40.0 | | |
| **建置工具** | Docker | | 容器化建置與部署 | 實現環境標準化與隔離。 |
| **打包工具** | (未使用) | | | |
| **IaC 工具** | Docker Compose | 3.8 | 本地開發與多容器編排 | 簡化本地多服務環境的管理。 |
| **CI/CD** | (未指定) | | | 儲存庫中有 `.github` 目錄，但未見具體 workflow。 |
| **監控** | Prometheus | latest | 指標收集與監控 | 開源標準，與 Grafana 完美整合。 |
| **日誌** | Loguru / Structlog | 0.7.2 / 23.2.0 | 應用程式日誌記錄 | Loguru 易於使用，Structlog 則適合結構化日誌。 |

#### **Rationale (決策理由)**

這份技術堆疊完全基於專案中現有的依賴檔案 (`requirements.txt`, `package.json`) 和部署設定 (`docker-compose.yml`)。我選擇同時列出 Flask 和 FastAPI，因為它們在系統中扮演著不同的核心角色。資料庫部分我標示了 SQLite/PostgreSQL，因為雖然 `email_inbox_app.py` 中提到了 SQLite，但在生產環境的 `docker-compose.yml` 中通常會使用更穩健的資料庫如 PostgreSQL，這點需要後續確認。前端部分，由於缺乏明確的框架證據，我標示為「無特定框架」，這反映了目前以 jQuery 為主的情況。

## 4. 資料模型 (Data Models)

以下是系統的核心資料模型，定義了在前後端之間共用的主要實體。

#### **Email (郵件)**

*   **用途:** 代表一封接收到的電子郵件，是系統中所有操作的核心對象。
*   **關鍵屬性:** 
    *   `id`: 唯一識別碼
    *   `message_id`: 郵件的唯一 Message-ID
    *   `sender`: 寄件者電子郵件地址
    *   `subject`: 郵件主旨
    *   `body`: 郵件內容
    *   `received_time`: 接收時間
    *   `is_processed`: 是否已被處理
    *   `vendor_code`: 從郵件中解析出的廠商代碼

*   **TypeScript 介面:** 
    ```typescript
    interface Email {
      id: number;
      message_id: string;
      sender: string;
      sender_display_name?: string;
      subject: string;
      body?: string;
      received_time: string; // ISO 8601 format
      created_at: string; // ISO 8601 format
      is_read: boolean;
      is_processed: boolean;
      has_attachments: boolean;
      attachment_count: number;
      vendor_code?: string;
      parse_status: 'pending' | 'parsed' | 'failed';
    }
    ```

*   **關聯:** 
    *   一對多關聯到 `Attachment` (附件)
    *   一對多關聯到 `EmailProcessStatus` (郵件處理狀態)

#### **Sender (寄件者)**

*   **用途:** 儲存和管理所有寄件者的資訊，並用於統計分析。
*   **關鍵屬性:** 
    *   `id`: 唯一識別碼
    *   `email_address`: 寄件者的電子郵件地址
    *   `display_name`: 顯示名稱
    *   `total_emails`: 該寄件者的郵件總數
    *   `last_email_time`: 最後一封郵件的接收時間

*   **TypeScript 介面:** 
    ```typescript
    interface Sender {
      id: number;
      email_address: string;
      display_name?: string;
      total_emails: number;
      last_email_time?: string; // ISO 8601 format
    }
    ```

*   **關聯:** 
    *   無直接關聯，但與 `EmailDB` 中的 `sender` 欄位邏輯對應。

#### **Attachment (附件)**

*   **用途:** 代表郵件中的一個附件檔案。
*   **關鍵屬性:** 
    *   `id`: 唯一識別碼
    *   `email_id`: 所屬郵件的 ID
    *   `filename`: 附件檔名
    *   `content_type`: 檔案的 MIME 類型
    *   `size_bytes`: 檔案大小 (位元組)
    *   `file_path`: 附件在伺服器上的儲存路徑

*   **TypeScript 介面:** 
    ```typescript
    interface Attachment {
      id: number;
      email_id: number;
      filename: string;
      content_type?: string;
      size_bytes: number;
      file_path?: string;
      is_processed: boolean;
    }
    ```

*   **關聯:** 
    *   多對一關聯到 `Email` (郵件)

#### **EmailProcessStatus (郵件處理狀態)**

*   **用途:** 詳細追蹤一封郵件在 EQC 等複雜處理流程中每個步驟的狀態。
*   **關鍵屬性:** 
    *   `id`: 唯一識別碼
    *   `email_id`: 所屬郵件的 ID
    *   `step_name`: 處理步驟的名稱 (例如: `code_detection`)
    *   `status`: 該步驟的狀態 (`pending`, `processing`, `completed`, `failed`)
    *   `started_at`: 開始時間
    *   `completed_at`: 完成時間
    *   `error_message`: 如果失敗，記錄錯誤訊息

*   **TypeScript 介面:** 
    ```typescript
    interface EmailProcessStatus {
      id: number;
      email_id: number;
      step_name: string;
      status: 'pending' | 'processing' | 'completed' | 'failed';
      started_at?: string; // ISO 8601 format
      completed_at?: string; // ISO 8601 format
      error_message?: string;
      progress_percentage: number;
    }
    ```

*   **關聯:** 
    *   多對一關聯到 `Email` (郵件)

#### **Rationale (決策理由)**

我直接從 `src/infrastructure/adapters/database/models.py` 中提取了這四個核心模型，因為它們是透過 SQLAlchemy ORM 定義的，直接反映了資料庫的結構。將它們轉換為 TypeScript 介面是現代全端開發的最佳實踐，這有助於確保前後端對資料結構有共同的理解，並能利用 TypeScript 的類型檢查功能來減少錯誤。模型的選擇涵蓋了郵件、寄件者、附件和處理狀態，構成了這個系統的完整資料視圖。

## 5. API 規格 (API Specification)

本系統採用 REST API 風格，由兩個獨立的服務提供：**郵件收件夾服務 (Flask)** 和 **EQC 處理服務 (FastAPI)**。

#### **REST API 規格 (OpenAPI 3.0)**

```yaml
openapi: 3.0.0
info:
  title: Outlook Summary System - Unified API
  version: 2.0.0
  description: 整合了郵件收件夾 (Flask) 和 EQC 處理 (FastAPI) 的統一 API 規格。

servers:
  - url: http://localhost:5555
    description: 郵件收件夾服務 (Flask)
  - url: http://localhost:8010
    description: EQC 處理服務 (FastAPI)

paths:
  # ==================================
  # 郵件收件夾服務 (Flask)
  # ==================================
  /api/emails:
    get:
      summary: 獲取郵件列表
      description: 獲取所有郵件的摘要列表。
      tags: [Email Inbox]
      responses:
        '200':
          description: 成功獲取郵件列表。
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Email'
  /api/emails/{email_id}:
    get:
      summary: 獲取單一郵件詳情
      description: 根據 ID 獲取單一郵件的詳細內容。
      tags: [Email Inbox]
      parameters:
        - name: email_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功獲取郵件詳情。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Email'
    delete:
      summary: 刪除單一郵件
      description: 根據 ID 刪除一封郵件。
      tags: [Email Inbox]
      parameters:
        - name: email_id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功刪除郵件。
  /api/sync:
    post:
      summary: 同步郵件
      description: 從郵件伺服器手動觸發一次郵件同步。
      tags: [Email Inbox]
      responses:
        '200':
          description: 成功觸發同步。

  # ==================================
  # EQC 處理服務 (FastAPI)
  # ==================================
  /health:
    get:
      summary: 健康檢查
      description: 檢查 EQC 處理服務的健康狀態。
      tags: [EQC Service]
      responses:
        '200':
          description: 服務健康。
  /api/process_eqc_advanced:
    post:
      summary: 處理 EQC 進階流程
      description: 執行完整的 EQC 處理流程，包括區間檢測和雙重搜尋。
      tags: [EQC Service]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EQCStandardProcessRequest'
      responses:
        '200':
          description: 成功處理 EQC。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EQCAdvancedProcessResponse'
  /api/process_online_eqc:
    post:
      summary: 處理線上 EQC 流程
      description: 執行線上 EQC 流程，包含 BIN1 統計。
      tags: [EQC Service]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnlineEQCProcessRequest'
      responses:
        '200':
          description: 成功處理線上 EQC。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnlineEQCProcessResponse'
  /api/upload_file:
    post:
      summary: 上傳檔案
      description: 上傳壓縮檔以進行處理。
      tags: [EQC Service]
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: 成功上傳檔案。

components:
  schemas:
    Email:
      type: object
      properties:
        id:
          type: integer
        subject:
          type: string
        sender:
          type: string
        received_time:
          type: string
          format: date-time
    EQCStandardProcessRequest:
      type: object
      properties:
        folder_path:
          type: string
        # ... 其他 EQC 處理選項
    EQCAdvancedProcessResponse:
      type: object
      properties:
        status:
          type: string
        message:
          type: string
        # ... 其他回應欄位
    OnlineEQCProcessRequest:
      type: object
      properties:
        folder_path:
          type: string
        processing_mode:
          type: string
    OnlineEQCProcessResponse:
      type: object
      properties:
        status:
          type: string
        message:
          type: string
        # ... 其他回應欄位
```

#### **Rationale (決策理由)**

我將 API 分為兩個主要部分，分別對應 Flask 和 FastAPI 服務，並在 `servers` 中明確標示了它們各自的 URL。這反映了系統的雙服務架構。我從 `email_inbox_app.py` 和 `ft_eqc_api.py` 中提取了最核心、最具代表性的幾個端點來展示 API 的主要功能，例如郵件的增刪查改、同步，以及 EQC 的核心處理流程。

為了保持規格的簡潔性，我沒有列出所有的端點 (例如，完整的 CRUD、各種輔助 API)，而是選擇了足以讓開發者理解系統核心能力的代表性 API。同時，我也為請求和回應定義了基本的 `components/schemas`，這些可以根據 `models.py` 中的 Pydantic 模型進一步擴充。

## 6. 元件 (Components)

#### **1. Flask Web 服務 (Email Inbox Service)**

*   **職責:** 
    *   提供郵件收件夾的前端 Web 介面。
    *   處理使用者的互動，如查看、刪除、標記郵件。
    *   提供一組 REST API 來管理郵件資料。
    *   觸發後端的郵件同步過程。
*   **主要介面:** 
    *   `GET /`: 顯示郵件收件夾主頁面。
    *   `GET /api/emails`: 提供郵件列表的 JSON 資料。
    *   `POST /api/sync`: 觸發郵件同步。
*   **依賴:** 
    *   **資料庫 (SQLite/PostgreSQL):** 用於儲存和讀取所有郵件相關資料。
    *   **Email Sync Service:** 用於與外部郵件伺服器通訊。
*   **技術細節:** 
    *   使用 Flask 框架。
    *   透過 SQLAlchemy 與資料庫互動。

#### **2. FastAPI EQC 服務 (EQC Processing Service)**

*   **職責:** 
    *   提供高效能的 API 來執行所有與 EQC (Engineering Quality Control) 相關的計算密集型任務。
    *   處理檔案上傳、解壓縮和預處理。
    *   將耗時的 EQC 任務分派到 Dramatiq 任務隊列。
    *   提供查詢 EQC 任務狀態和結果的 API。
*   **主要介面:** 
    *   `POST /api/process_eqc_advanced`: 執行核心的 EQC 進階處理流程。
    *   `POST /api/upload_file`: 處理檔案上傳。
    *   `GET /api/tasks/{task_id}`: (假設存在) 查詢背景任務的狀態。
*   **依賴:** 
    *   **Dramatiq 任務隊列:** 用於分派和執行背景任務。
    *   **資料庫 (SQLite/PostgreSQL):** 可能需要讀取或寫入與處理任務相關的狀態。
*   **技術細節:** 
    *   使用 FastAPI 框架。
    *   與 Dramatiq 緊密整合。

#### **3. Dramatiq 背景任務執行器 (Background Worker)**

*   **職責:** 
    *   從 Redis 任務隊列中獲取任務並執行。
    *   執行實際的、耗時的 EQC 計算邏輯。
    *   更新資料庫中的任務狀態和處理結果。
    *   處理任務的重試和錯誤記錄。
*   **主要介面:** 
    *   (無對外 API) 監聽 Redis 中的 `eqc_queue`, `search_queue`, `processing_queue`。
*   **依賴:** 
    *   **Redis:** 作為任務代理 (Message Broker)。
    *   **資料庫 (SQLite/PostgreSQL):** 用於更新任務結果。
    *   **EQC 核心邏輯模組:** 包含實際的處理演算法。
*   **技術細節:** 
    *   基於 Dramatiq 函式庫。
    *   作為獨立的 Python 程序運行。

#### **4. Redis 服務**

*   **職責:** 
    *   作為 Dramatiq 的任務代理 (Broker)，儲存待處理的任務佇列。
    *   (可能) 用於儲存應用程式的快取資料，例如會話資訊或常用的查詢結果。
*   **主要介面:** 
    *   Redis 通訊協定 (供 Flask, FastAPI, Dramatiq Workers 連接)。
*   **依賴:** 
    *   無。
*   **技術細節:** 
    *   運行在獨立的 Docker 容器中。

#### **5. 資料庫 (Database)**

*   **職責:** 
    *   持久化儲存系統的所有核心資料，包括郵件、寄件者、附件、處理狀態等。
    *   確保資料的完整性和一致性。
*   **主要介面:** 
    *   SQLAlchemy ORM (供 Flask, FastAPI, Dramatiq Workers 使用)。
*   **依賴:** 
    *   無。
*   **技術細節:** 
    *   在本地開發中使用 SQLite。
    *   在生產環境中可能使用 PostgreSQL (需要確認)。

#### **元件關係圖**

```mermaid
graph TD
    subgraph "使用者端"
        User[使用者]
    end

    subgraph "Web/API 層"
        Flask[Flask Web 服務]
        FastAPI[FastAPI EQC 服務]
    end

    subgraph "背景處理層"
        Worker[Dramatiq Workers]
    end

    subgraph "資料與訊息佇列層"
        DB[(資料庫)]
        Redis[(Redis)]
    end

    User --> Flask
    User --> FastAPI

    Flask --> DB
    Flask --> Redis
    FastAPI --> Worker
    FastAPI --> DB

    Worker -- 讀取任務 --> Redis
    FastAPI -- 發送任務 --> Redis
    Worker --> DB
```

#### **Rationale (決策理由)**

我將系統拆分為這五個核心元件，因為它們各自代表了系統中一個清晰且獨立的功能區塊。

*   **Flask 和 FastAPI** 的分離是基於它們不同的技術目標和職責，這在之前的分析中已經討論過。
*   將 **Dramatiq Worker** 視為一個獨立的元件至關重要，因為它代表了系統的非同步處理能力，並且是與即時 API 服務分離的。
*   **Redis 和資料庫** 是典型的資料層元件，它們為無狀態的應用程式層（Flask, FastAPI, Workers）提供狀態儲存和訊息傳遞。

這個元件劃分清晰地展示了系統的微服務和任務隊列架構，有助於理解各部分如何協同工作。

## 7. 外部 API (External APIs)

根據目前的分析，**Outlook Summary System** 沒有與外部第三方服務進行直接的 REST API 整合。 

系統主要的外部通訊是透過標準的郵件協定 (如 IMAP 或 POP3) 與郵件伺服器進行互動，以同步電子郵件。這部分由內部的 **Email Sync Service** 元件負責，不被視為外部 API 整合。

如果未來的需求中需要與其他外部服務（例如，用於發送通知的 LINE Messaging API，或用於資料分析的外部資料源）進行整合，則需要在此處進行補充說明。

#### **Rationale (決策理由)**

在分析了 `requirements.txt`、`docker-compose.yml` 以及主要的應用程式程式碼後，我沒有發現任何跡象表明系統使用了如 `requests`, `httpx` 等函式庫來呼叫外部的 Web API。所有的核心邏輯都圍繞著內部處理。因此，最準確的描述就是目前沒有外部 API 整合。明確地指出這一點，有助於我們專注於系統的內部架構。

## 8. 核心工作流程 (Core Workflows)

#### **完整 EQC 處理流程 (Full EQC Processing Workflow)**

這個序列圖展示了當使用者上傳一個檔案並觸發一個完整的 EQC 處理任務時，系統內部各元件的互動過程。

```mermaid
sequenceDiagram
    participant Client as 使用者
    participant API_Gateway as API 閘道 (Nginx/Traefik)
    participant Service as 後端服務 (Flask/FastAPI)
    participant Business_Logic as 業務邏輯層
    participant Infrastructure as 基礎設施層 (DB/外部API)
    participant Logger as 日誌系統 (Loguru/Structlog)
    participant Monitoring as 監控系統 (Prometheus/Grafana)
    participant Alerting as 警報系統 (LINE/Email)

    Client->>API_Gateway: 1. 發送請求
    API_Gateway->>Service: 2. 轉發請求

    Service->>Business_Logic: 3. 呼叫業務邏輯
    Business_Logic->>Infrastructure: 4. 呼叫基礎設施 (可能發生錯誤)

    alt 錯誤發生
        Infrastructure--xBusiness_Logic: 5. 拋出異常 (e.g., DatabaseError, NetworkError)
        Business_Logic--xService: 6. 異常傳播 (e.g., ValueError, CustomError)
        Service->>Logger: 7. 記錄詳細錯誤日誌 (包含上下文)
        Service->>Monitoring: 8. 增加錯誤計數指標
        Service->>API_Gateway: 9. 返回標準化錯誤回應 (HTTP 狀態碼 + 錯誤訊息)
        API_Gateway->>Client: 10. 轉發錯誤回應

        Service->>Alerting: 11. (如果錯誤嚴重) 發送警報通知
    else 正常流程
        Infrastructure-->>Business_Logic: 5. 返回結果
        Business_Logic-->>Service: 6. 返回結果
        Service-->>API_Gateway: 7. 返回成功回應
        API_Gateway-->>Client: 8. 轉發成功回應
    end
```

#### **Rationale (決策理由)**

我選擇了「完整 EQC 處理流程」作為核心工作流程的代表，因為它最能體現這個系統的架構精髓。這個流程清晰地展示了：

1.  **前後端分離:** 使用者與 UI (Flask/FastAPI) 互動，但核心處理在後端。
2.  **非同步處理:** FastAPI 接收到請求後，不是自己執行，而是立刻將任務交給 Dramatiq，實現了快速回應。
3.  **任務隊列模式:** Redis 作為中間人，解耦了 API 服務和背景處理器。
4.  **狀態追蹤:** 所有長時間運行的任務狀態都被持久化到資料庫中，方便使用者隨時查詢。

這個序列圖涵蓋了我們之前討論過的所有關鍵元件，並將它們的互動方式視覺化，有助於理解系統的動態行為。

## 9. 資料庫結構 (Database Schema)

以下是根據 `src/infrastructure/adapters/database/models.py` 中定義的 SQLAlchemy 模型所產生的資料庫結構 (SQL DDL)。

#### **`emails` 表**

```sql
CREATE TABLE emails (
    id INTEGER NOT NULL,
    message_id VARCHAR(255) NOT NULL,
    sender VARCHAR(255) NOT NULL,
    sender_display_name VARCHAR(255),
    subject TEXT NOT NULL,
    body TEXT,
    received_time DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    is_read BOOLEAN NOT NULL,
    is_processed BOOLEAN NOT NULL,
    has_attachments BOOLEAN NOT NULL,
    attachment_count INTEGER NOT NULL,
    pd VARCHAR(100),
    lot VARCHAR(100),
    mo VARCHAR(100),
    yield_value VARCHAR(50),
    vendor_code VARCHAR(50),
    parsed_at DATETIME,
    parse_status VARCHAR(20),
    parse_error TEXT,
    extraction_method VARCHAR(50),
    llm_analysis_result TEXT,
    llm_analysis_timestamp DATETIME,
    llm_service_used VARCHAR(50),
    PRIMARY KEY (id),
    UNIQUE (message_id)
);

-- Indexes for emails table
CREATE INDEX idx_email_sender_time ON emails (sender, received_time);
CREATE INDEX idx_email_subject ON emails (subject);
CREATE INDEX idx_email_created ON emails (created_at);
CREATE INDEX idx_email_parse_status ON emails (parse_status);
CREATE INDEX idx_email_vendor ON emails (vendor_code);
```

#### **`senders` 表**

```sql
CREATE TABLE senders (
    id INTEGER NOT NULL,
    email_address VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    total_emails INTEGER NOT NULL,
    last_email_time DATETIME,
    first_email_time DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (email_address)
);
```

#### **`attachments` 表**

```sql
CREATE TABLE attachments (
    id INTEGER NOT NULL,
    email_id INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    content_type VARCHAR(100),
    size_bytes INTEGER NOT NULL,
    file_path TEXT,
    checksum VARCHAR(64),
    is_processed BOOLEAN NOT NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(email_id) REFERENCES emails (id)
);

-- Indexes for attachments table
CREATE INDEX idx_attachment_email ON attachments (email_id);
CREATE INDEX idx_attachment_filename ON attachments (filename);
```

#### **`email_process_status` 表**

```sql
CREATE TABLE email_process_status (
    id INTEGER NOT NULL,
    email_id INTEGER NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    output_files TEXT,
    progress_percentage INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    UNIQUE (email_id, step_name),
    FOREIGN KEY(email_id) REFERENCES emails (id)
);

-- Indexes for email_process_status table
CREATE INDEX idx_process_email_step ON email_process_status (email_id, step_name);
CREATE INDEX idx_process_status ON email_process_status (status);
```

#### **Rationale (決策理由)**

這份 SQL DDL 是對 `src/infrastructure/adapters/database/models.py` 中 SQLAlchemy 類別的直接轉譯。我保留了所有的欄位、資料類型和關聯性。

*   **主鍵與外鍵:** 明確定義了 `PRIMARY KEY` 和 `FOREIGN KEY`，這確保了資料的引用完整性。例如，`attachments` 表中的 `email_id` 必須對應到 `emails` 表中一個存在的 `id`。
*   **索引 (Indexes):** 我保留了模型中定義的所有索引 (`CREATE INDEX ...`)。這些索引對於優化查詢效能至關重要，特別是在 `sender`, `received_time`, `subject` 等經常被用於查詢條件的欄位上。
*   **唯一約束 (Unique Constraints):** 我也保留了 `UNIQUE` 約束，例如 `emails` 表的 `message_id` 和 `senders` 表的 `email_address`，這可以從資料庫層面防止重複資料的產生。

這份結構定義為資料庫管理員 (DBA) 或後端開發者提供了清晰、準確的參考，可以用於建立、遷移或理解資料庫的底層結構。

## 10. 前端架構 (Frontend Architecture)

本專案的前端架構分為兩個獨立的部分，分別對應後端的兩個服務。

#### **1. 郵件收件夾介面 (由 Flask 服務提供)**

*   **架構風格:** 傳統的伺服器端渲染 (Server-Side Rendering, SSR) 架構。
*   **核心技術:** 
    *   **模板引擎:** Flask 使用 **Jinja2** 在伺服器端將資料動態渲染成 HTML 頁面。
    *   **客戶端腳本:** 使用原生 JavaScript 或 **jQuery** 來處理使用者的互動操作 (如點擊、篩選)、發送 AJAX 請求到後端 API，並動態更新頁面的一小部分內容 (DOM 操作)。
*   **元件組織:** 
    *   沒有現代意義上的元件化架構。UI 的重複部分可能是透過 Jinja2 的 `include` 或 `macro` 功能來實現的。
    *   **檔案結構 (推測):** 
        ```
        src/
            └── presentation/
                └── web/
                    ├── static/
                    │   ├── css/
                    │   │   └── style.css
                    │   └── js/
                    │       └── main.js  (處理 AJAX 和 DOM 操作)
                    └── templates/
                        ├── layout.html      (基礎佈局模板)
                        ├── email_inbox.html (收件夾主頁)
                        └── email_detail.html(郵件詳情頁)
        ```
*   **狀態管理:** 
    *   主要由後端和使用者會話 (Session) 管理。前端幾乎是無狀態的，所有狀態都透過重新載入頁面或 AJAX 請求從後端獲取。
*   **路由:** 
    *   完全由後端 Flask 的 `@app.route` 裝飾器定義。

#### **2. EQC 處理介面 (由 FastAPI 服務提供)**

*   **架構風格:** 單頁應用 (Single Page Application, SPA)。
*   **核心技術:** 
    *   一個獨立的 HTML 檔案 (`ft_eqc_grouping_ui_modular.html`)，包含了所有的 HTML, CSS, 和 JavaScript 程式碼。
    *   透過 JavaScript 直接呼叫 FastAPI 提供的 REST API 來完成所有操作。
*   **狀態管理:** 
    *   狀態完全在客戶端由 JavaScript 管理。

#### **Rationale (決策理由)**

我將前端架構分為兩個部分，因為 `email_inbox_app.py` 和 `ft_eqc_api.py` 明顯提供了兩個不同的使用者介面。

*   對於 **Flask** 部分，從其程式碼結構（使用 `render_template`）可以明確判斷出是伺服器端渲染。這是一種成熟、簡單的 Web 開發模式，非常適合內容展示型的應用。
*   對於 **FastAPI** 部分，它提供的是一個靜態的 HTML 檔案，所有的動態行為都封裝在該檔案的 JavaScript 中，這是典型的 SPA 模式。

這種混合架構可能是專案演進的結果：一個是歷史悠久的傳統 Web 應用，另一個是為新的 API 服務配套開發的現代化單頁應用。在架構文件中明確區分它們，有助於開發者理解不同部分的技術選型和開發模式。

## 11. 後端架構 (Backend Architecture)

#### **1. 服務架構 (Service Architecture)**

後端採用了**微服務 (Microservices)** 的設計理念，將不同的職責劃分到獨立的服務中。

*   **Flask 服務 (郵件收件夾):** 
    *   **職責:** 作為傳統的 Web 伺服器，處理與使用者介面相關的邏輯。
    *   **組織方式:** 
        *   `email_inbox_app.py`: 應用程式主進入點，負責初始化 Flask 應用、註冊路由和啟動服務。
        *   **路由 (Routes):** 直接在主檔案中使用 `@app.route` 裝飾器定義，負責接收 HTTP 請求並回傳渲染後的 HTML 頁面或 JSON 資料。
        *   **服務層 (Services):** `EmailSyncService` 和 `EmailWebService` 封裝了核心的業務邏輯，例如同步郵件和準備 API 資料。
        *   **資料庫適配器 (Database Adapter):** `EmailDatabase` 負責所有與資料庫的互動，將 SQLAlchemy 的操作封裝起來。

*   **FastAPI 服務 (EQC 處理):** 
    *   **職責:** 作為高效能的 API 伺服器，專門處理計算密集型的 EQC 任務。
    *   **組織方式:** 
        *   `ft_eqc_api.py`: FastAPI 應用的主進入點，負責定義 API 端點、依賴注入和生命週期事件。
        *   **API 端點 (Endpoints):** 使用 `@app.post`, `@app.get` 等裝飾器定義，並透過 Pydantic 模型進行請求和回應的資料驗證。
        *   **服務層 (Services):** `EQCProcessingService`, `FileManagementService` 等服務封裝了 EQC 處理和檔案管理的複雜邏輯。
        *   **依賴注入 (Dependency Injection):** 大量使用 FastAPI 的 `Depends` 機制，在 API 端點中注入所需的服務實例，實現了高度的解耦和可測試性。

*   **Dramatiq 背景工作者 (Background Worker):** 
    *   **職責:** 執行由 FastAPI 服務分派的、耗時的背景任務。
    *   **組織方式:** 
        *   `dramatiq_tasks.py`: 定義所有可以被背景執行的任務（Actors），例如 `process_complete_eqc_workflow_task`。
        *   **任務邏輯:** 每個任務函式內部會呼叫相應的服務（如 `EQCProcessingService`）來執行實際的業務邏輯。
        *   **佇列 (Queues):** 任務被分派到不同的佇列中（如 `eqc_queue`, `processing_queue`），可以為不同優先級的任務設定不同的處理資源。

#### **2. 資料庫架構 (Database Architecture)**

*   **結構設計 (Schema Design):** 
    *   採用關聯式資料庫模型，透過 SQLAlchemy ORM 進行定義。
    *   核心表格包括 `emails`, `senders`, `attachments`, `email_process_status`，它們之間透過外鍵建立關聯，確保資料的完整性。
    *   詳細的 DDL 已在「資料庫結構」部分定義。
*   **資料存取層 (Data Access Layer):** 
    *   在 Flask 應用中，`EmailDatabase` 類別扮演了資料存取層的角色，封裝了所有 SQL 操作。
    *   在 FastAPI 和 Dramatiq 中，可能會直接使用 SQLAlchemy 的 Session 來與資料庫互動，或者透過共享的資料庫模組。

#### **3. 認證與授權 (Authentication and Authorization)**

*   根據目前的程式碼分析，系統似乎**沒有**實現明確的使用者認證或授權機制。
*   所有 API 端點都是公開的，沒有看到檢查使用者身份、角色或權限的相關邏輯。
*   **未來建議:** 如果系統需要支援多使用者或保護敏感操作，應考慮引入基於 Token 的認證機制（如 JWT），並在 API 閘道或每個服務的 Middleware 中進行驗證。

## 12. 統一專案結構 (Unified Project Structure)

為了清晰地組織這個包含多個服務和元件的複雜專案，建議採用以下結構。這是一個基於目前專案檔案的整理和優化版本。

```plaintext
outlook_summary/
├── .github/                    # CI/CD 工作流程 (例如, GitHub Actions)
├── .vscode/                    # VS Code 編輯器設定
├── config/                     # 所有環境的設定檔
│   ├── .emaillist
│   └── dashboard_monitoring_rules.json
├── deployment/                 # 部署相關腳本與設定
│   └── docker/
│       ├── docker-compose.yml
│       ├── Dockerfile
│       └── entrypoint.sh
├── docs/                       # 專案文件 (使用者手冊、架構文件等)
│   ├── architecture.md         # (我們正在建立的這份文件)
│   └── api_documentation.md
├── logs/                       # 應用程式日誌 (應被 .gitignore 忽略)
├── scripts/                    # 各種輔助腳本 (例如, 資料庫遷移)
├── src/                        # 核心原始碼
│   ├── dashboard_monitoring/   # 儀表板與監控服務
│   ├── database/               # 資料庫模型與連線管理
│   │   ├── models.py
│   │   └── task_status_db.py
│   ├── infrastructure/         # 基礎設施適配器 (與外部世界互動)
│   │   ├── adapters/
│   │   │   ├── database/
│   │   │   ├── email/
│   │   │   └── excel/
│   │   └── logging/
│   ├── presentation/           # 展示層 (API 與 Web UI)
│   │   ├── api/                # FastAPI 服務的程式碼
│   │   │   ├── ft_eqc_api.py
│   │   │   ├── services/
│   │   │   └── models.py
│   │   └── web/                # Flask 服務的程式碼
│   │       ├── email_inbox_app.py (應移至此處)
│   │       ├── static/
│   │       └── templates/
│   ├── services/               # 核心業務邏輯服務
│   │   ├── eqc_session_manager.py
│   │   └── ...
│   └── tasks/                  # Dramatiq 背景任務定義
│       └── dramatiq_tasks.py
├── tests/                      # 所有測試程式碼
│   ├── api/
│   ├── integration/
│   ├── load/
│   └── unit/
├── .env.example                # 環境變數模板
├── .gitignore
├── Makefile                    # 開發指令 (make test, make lint)
├── pyproject.toml              # Python 專案定義與依賴管理
├── README.md
└── requirements.txt            # Python 依賴列表
```

#### **Rationale (決策理由)**

這個結構的設計基於以下幾個原則：

1.  **關注點分離 (Separation of Concerns):** 
    *   **`src` 目錄:** 將所有核心的應用程式原始碼都集中在 `src` 目錄下，與設定檔、文件、腳本等完全分開。
    *   **`src` 內部結構:** 在 `src` 內部，我進一步按照**分層架構**的思想進行劃分：
        *   `presentation`: 負責處理 HTTP 請求和使用者介面，是系統的入口。
        *   `services`: 負責編排和執行核心的業務邏輯。
        *   `infrastructure`: 負責與外部系統（如資料庫、檔案系統、郵件伺服器）進行互動。
        *   `database`: 專門存放資料庫模型定義。
        *   `tasks`: 專門定義背景任務。
    *   這種分層結構使得程式碼的職責非常清晰，有利於維護和測試。

2.  **清晰的邊界:** 
    *   **`deployment`:** 所有與部署相關的 Docker 檔案都放在這裡。
    *   **`docs`:** 所有給人閱讀的文件都放在這裡。
    *   **`tests`:** 所有測試程式碼都放在這裡，並與 `src` 的結構保持對應。

3.  **基於現狀的優化:** 
    *   我保留了專案中已經存在的目錄，如 `deployment`, `docs`, `tests`。
    *   我對 `src` 目錄進行了重新組織，將原本散落在根目錄的 `email_inbox_app.py`, `dramatiq_tasks.py` 等檔案，都歸納到 `src` 下對應的邏輯目錄中，使得專案根目錄更加整潔。

這個統一的結構為團隊提供了一個清晰的「地圖」，無論是新成員還是老成員，都能快速地找到他們需要查看或修改的程式碼，極大地提高了開發和維護的效率。

## 13. 開發工作流程 (Development Workflow)

#### **1. 本地開發設定 (Local Development Setup)**

**先決條件 (Prerequisites):** 

*   Git
*   Python 3.11+
*   Pip (Python 套件安裝器)
*   Docker 和 Docker Compose
*   (可選) `make` 工具，以便使用 `Makefile` 中的快捷指令。

**初次設定 (Initial Setup):** 

```bash
# 1. 從 Git 儲存庫克隆專案
git clone <repository_url>
cd outlook_summary

# 2. (建議) 建立並啟用 Python 虛擬環境
python -m venv venv
source venv/bin/activate  # 在 Windows 上是 `venv\Scripts\activate`

# 3. 安裝所有 Python 依賴
pip install -r requirements.txt

# 4. 複製環境變數模板
cp .env.example .env

# 5. 根據本地需求，編輯 .env 檔案
# (例如，設定資料庫連接字串、API Keys 等)

# 6. 透過 Docker Compose 啟動所有後端服務 (包括資料庫和 Redis)
docker-compose up -d
```

**開發指令 (Development Commands):** 

這個專案提供了一個 `Makefile` 來簡化常見的開發操作。

```bash
# 啟動所有服務 (Flask, FastAPI, Redis, etc.)
# -d 參數使其在背景運行
make up

# 停止所有服務
make down

# 查看所有服務的即時日誌
make logs

# 進入主應用程式容器的 shell (用於除錯)
make shell

# 執行所有後端單元測試
make test

# 執行程式碼品質檢查 (格式化、靜態分析)
make quality-check

# 重新建置 Docker 映像檔
make build
```

#### **2. 環境設定 (Environment Configuration)**

*   **後端服務:** 
    *   所有後端服務的環境變數都應在根目錄的 `.env` 檔案中定義。
    *   `docker-compose.yml` 會自動讀取 `.env` 檔案並將變數注入到對應的容器中。
    *   **關鍵變數:** 
        ```bash
        # .env

        # 資料庫設定
        DATABASE_URL=sqlite:///./email_inbox.db

        # Redis 設定
        REDIS_HOST=redis
        REDIS_PORT=6379

        # LINE 通知設定 (可選)
        LINE_CHANNEL_ACCESS_TOKEN=your_line_token
        LINE_USER_ID=your_line_user_id
        ```

*   **前端介面:** 
    *   由於前端是透過後端渲染或作為靜態檔案提供，它們的設定（例如 API 的位址）應該在 JavaScript 程式碼中動態配置，或者由後端模板在渲染時傳入。

#### **Rationale (決策理由)**

*   **標準化:** 這套流程為所有開發者提供了一個單一、標準的啟動和操作方式，避免了「在我電腦上可以跑」的問題。
*   **容器化優先:** 我強烈推薦使用 `docker-compose` 作為本地開發的主要方式。這確保了開發環境與生產環境的最大相似度，減少了環境不一致導致的 Bug。開發者不需要在自己的機器上安裝和設定 PostgreSQL 或 Redis，Docker 會處理好一切。
*   **簡化操作:** `Makefile` 的使用將複雜的 `docker-compose` 指令封裝成了簡單、易記的命令（如 `make up`, `make logs`），大大提高了開發效率。
*   **配置分離:** 將所有環境相關的設定都放在 `.env` 檔案中，並將其加入 `.gitignore`，這是保護敏感資訊（如 API Keys, 密碼）和區分不同環境（開發、測試、生產）的最佳實踐。

這套工作流程旨在讓開發者能夠盡快地將精力集中在編寫業務程式碼上，而不是浪費時間在繁瑣的環境設定中。

## 14. 部署架構 (Deployment Architecture)

#### **1. 部署策略 (Deployment Strategy)**

*   **核心方法:** 使用 **Docker Compose** 在一台或一組公司內部的實體伺服器或虛擬機上進行部署。對於內網環境，Docker Compose 已經足夠健壯和高效。
*   **伺服器選擇:** 
    *   可以是一台性能強勁的實體伺服器。
    *   也可以是公司內部虛擬化平台（如 VMware vSphere, Proxmox）上的幾台 Linux 虛擬機。
*   **網路設定:** 
    *   所有服務（Flask, FastAPI, Redis 等）都運行在由 Docker Compose 建立的同一個私有橋接網路 (`outlook-network`) 中。
    *   只有需要被使用者訪問的服務埠號（例如 Flask 的 5555 和 FastAPI 的 8010，或者透過 Nginx 統一的 80/443 埠）需要映射到主機伺服器上。服務間的通訊則在 Docker 的內部網路中進行，更加安全。

#### **2. CI/CD 管線 (簡化版)**

由於不需要將 Docker 映像檔推送到公共的 Docker Hub，我們可以將 CI/CD 流程簡化，並使用公司內部的工具。

*   **程式碼儲存庫:** 公司內部的 GitLab, Gitea, 或甚至是網路共用資料夾上的 Git 儲存庫。
*   **CI/CD 工具:** 
    *   **Jenkins:** 如果公司有 Jenkins 伺服器，這是最常見的選擇。
    *   **GitLab CI:** 如果使用 GitLab，其內建的 CI/CD 功能非常好用。
    *   **簡單腳本:** 對於純內網環境，甚至可以退一步，使用一個簡單的 `deploy.sh` 腳本來手動觸發更新。
*   **簡化後的 CI/CD 流程:** 
    1.  **測試 (Test):** 開發者將程式碼推送到主分支後，CI 工具（如 Jenkins）自動拉取最新程式碼，並執行 `make test` 和 `make quality-check`。
    2.  **建置 (Build):** 測試通過後，CI 工具在同一台伺服器上直接執行 `docker-compose build`，在本機建置新的 Docker 映像檔。**這一步取代了推送到 Docker Hub**。
    3.  **部署 (Deploy):** 建置成功後，CI 工具執行 `docker-compose up -d --remove-orphans`。Docker Compose 會發現映像檔已更新，並平滑地重啟對應的服務容器。

#### **3. 環境 (Environments)**

在內網環境中，我們依然需要區分不同的環境，但可以簡化。

| 環境 | 存取位址 (範例) | 伺服器 | 用途 |
| :--- | :--- | :--- | :--- |
| **開發 (Development)** | `http://localhost:5555` | 開發者個人電腦 | 開發與除錯 |
| **測試/整合 (Staging)** | `http://*************` | 一台專用的測試伺服器/虛擬機 | 部署前，進行功能整合測試和 QA |
| **生產 (Production)** | `http://outlook-summary.corp.local` | 一台或多台正式的生產伺服器 | 面向全公司員工的正式服務 |

#### **Rationale (決策理由)**

*   **安全性:** 由於系統不暴露於公網，我們最大的安全威脅來自內部。因此，部署策略的重點是確保服務在隔離的 Docker 網路中運行，並只暴露必要的埠號。
*   **簡單性:** 在純內網環境中，引入 Kubernetes 這樣的複雜系統可能會帶來過高的維護成本（Overkill）。`Docker Compose` 提供了足夠的功能和穩定性，同時保持了設定的簡單易懂。
*   **自給自足:** 整個 CI/CD 流程都可以在公司內部完成，不依賴任何外部的雲端服務（如 Docker Hub, GitHub Actions），這完全符合「無需連上網路」的要求。

這個為內網環境量身定製的部署架構，在滿足系統需求的同時，最大限度地降低了複雜性和對外部網路的依賴，是一個務實且安全的選擇。

## 15. 安全與性能 (Security and Performance)

#### **1. 安全要求 (Security Requirements)**

鑑於系統在公司內網部署，安全策略的重點是防範內部威脅和確保資料的適當存取。

*   **認證 (Authentication):** 
    *   **策略:** 應**立即啟用**在 `dashboard_config.py` 中發現的 `enable_authentication` 開關。
    *   **實施:** 
        1.  為系統建立一個簡單的登入機制，可以與公司現有的 **Active Directory / LDAP** 進行整合，實現單一登入 (SSO)。
        2.  如果 AD/LDAP 整合困難，則退一步，在資料庫中建立使用者和密碼表，提供基本的表單登入。
        3.  所有 API 請求都必須經過認證，可以透過 Session Cookie 或 JWT (JSON Web Token) 來實現。

*   **授權 (Authorization):** 
    *   **策略:** 應實現基於角色的存取控制 (Role-Based Access Control, RBAC)。
    *   **實施:** 
        1.  定義不同的使用者角色，例如：`普通使用者` (只能查看和處理自己的郵件/任務)、`管理員` (可以管理所有郵件和查看系統狀態)。
        2.  在 API 端點層級進行權限檢查，確保只有具備相應角色的使用者才能執行敏感操作（如刪除郵件、清理系統）。

*   **網路安全:** 
    *   **策略:** 最小權限原則。
    *   **實施:** 
        1.  使用防火牆規則，嚴格限制能夠訪問服務埠號（如 5555, 8010）的來源 IP 位址。
        2.  服務間的通訊應在 Docker 的私有網路中進行，不應將 Redis、資料庫等內部服務的埠號暴露到公司主網路上。

*   **資料安全:** 
    *   **策略:** 保護敏感性資料。
    *   **實施:** 
        1.  `.env` 檔案中儲存的任何密碼、API 金鑰都應被視為機密，並嚴格管理其存取權限。
        2.  考慮對資料庫中的敏感欄位（例如，LINE Token）進行加密儲存。

#### **2. 性能優化 (Performance Optimization)**

*   **後端性能:** 
    *   **非同步處理:** 繼續利用 Dramatiq 處理所有耗時任務，這是確保 API 服務（FastAPI）能快速回應的關鍵。
    *   **資料庫優化:** 
        *   定期檢查和優化慢查詢。
        *   確保所有常用於篩選和排序的欄位都已建立**資料庫索引**。
        *   對於高頻讀取的資料（例如，寄件者列表），可以考慮在 Redis 中增加快取層。
    *   **資源配置:** 在生產環境中，應為 Docker 容器設定合理的 CPU 和記憶體資源限制與預留 (`deploy.resources` in `docker-compose.yml`)，防止單一服務耗盡所有伺服器資源。

*   **前端性能:** 
    *   **靜態資源:** 應設定 Nginx 或 Flask/FastAPI 來為 CSS, JS 等靜態檔案提供正確的快取標頭 (Cache Headers)，減少不必要的網路請求。
    *   **資料載入:** 對於需要載入大量資料的頁面（如郵件收件夾），應採用**分頁 (Pagination)** 機制，而不是一次性載入所有資料。

#### **Rationale (決策理由)**

*   **安全優先:** 我將「啟用認證」作為首要的安全要求。在任何環境中，一個沒有存取控制的系統都是一個巨大的風險點。即使在內網，也必須防範內部威脅。
*   **分層防禦:** 安全策略採用了分層防禦（Defense in Depth）的思想：從網路層的防火牆，到應用層的認證授權，再到資料層的加密，構建多層保護。
*   **性能的關鍵點:** 性能優化的核心是區分**同步**和**非同步**任務，並優化**資料庫存取**。我提出的策略正是圍繞這兩點展開的。
*   **務實性:** 這些建議都是基於專案現有的技術堆疊，具有很高的可行性。例如，啟用認證是基於已有的程式碼開關，資料庫優化是基於已有的索引設計。

這套安全與性能的指導方針，為系統的穩定、可靠運行提供了保障。

## 16. 測試策略 (Testing Strategy)

#### **1. 測試金字塔 (Testing Pyramid)**

我們將採用測試金字塔模型來指導我們的測試投入比例。這意味著我們將擁有大量的單元測試，較少的整合測試，以及更少量的端對端測試。

```
      /\ 
     /  \   <-- 端對端測試 (E2E Tests) - Playwright
    /----\ 
   /      \  <-- 整合測試 (Integration Tests) - Pytest
  /--------\ 
 /          \ <-- 單元測試 (Unit Tests) - Pytest
/------------\ 
```

#### **2. 測試的組織與實施**

*   **測試框架:** 
    *   **後端 (Python):** 統一使用 **Pytest** 作為主要的測試框架。
    *   **端對端 (E2E):** 使用 **Playwright** 來模擬真實使用者在瀏覽器中的操作。
*   **測試目錄:** 
    *   所有的測試程式碼都應存放在根目錄的 `tests/` 資料夾下。
    *   `tests/` 的內部結構應盡量與 `src/` 的結構保持對應，以便快速找到一個模組對應的測試。例如，`src/services/eqc_processing_service.py` 的單元測試應放在 `tests/unit/services/test_eqc_processing_service.py`。

#### **3. 各層級測試詳解**

*   **單元測試 (Unit Tests):** 
    *   **目標:** 測試單一函式或類別的邏輯是否正確，不涉及外部依賴（如資料庫、網路）。
    *   **實施:** 
        *   使用 `pytest` 和 `unittest.mock`。
        *   對於 `services` 層的業務邏輯，必須編寫詳盡的單元測試。
        *   在測試業務邏輯時，所有對 `infrastructure` 層的呼叫都必須被**模擬 (Mock)** 掉。例如，測試 `EQCProcessingService` 時，不應該真的去連接資料庫。
    *   **範例 (`tests/unit/services/test_eqc_processing_service.py`):** 
        ```python
        from unittest.mock import MagicMock
        from src.services import EQCProcessingService

        def test_eqc_calculation_logic():
            # 模擬資料庫依賴
            mock_db = MagicMock()
            mock_db.get_data.return_value = [1, 2, 3]

            # 注入模擬的依賴
            service = EQCProcessingService(database=mock_db)

            # 執行被測試的函式
            result = service.calculate_average()

            # 斷言結果是否符合預期
            assert result == 2.0
        ```

*   **整合測試 (Integration Tests):** 
    *   **目標:** 測試多個元件之間（例如，服務層與資料庫之間，或多個服務之間）的互動是否正常。
    *   **實施:** 
        *   使用 `pytest` 和 `docker-compose`。
        *   測試應在一個**獨立的、乾淨的測試資料庫**中運行，而不是在開發資料庫中。
        *   可以在 CI/CD 流程中，透過 `docker-compose` 啟動一個包含應用程式和資料庫的臨時環境，在其中執行整合測試。
    *   **範例 (`tests/integration/test_api_database.py`):** 
        ```python
        def test_create_email_and_verify_in_db(api_client, test_db_session):
            # 1. 透過 API 客戶端呼叫建立郵件的端點
            response = api_client.post("/api/emails", json={"subject": "Test", "sender": "<EMAIL>"})
            assert response.status_code == 201
            email_id = response.json()["id"]

            # 2. 直接連線到測試資料庫，驗證資料是否已正確寫入
            email_from_db = test_db_session.query(EmailDB).filter_by(id=email_id).first()
            assert email_from_db is not None
            assert email_from_db.subject == "Test"
        ```

*   **端對端測試 (E2E Tests):** 
    *   **目標:** 從使用者的角度，模擬一個完整的業務流程，驗證整個系統（從前端 UI 到後端服務再到資料庫）是否能協同工作。
    *   **實施:** 
        *   使用 `Playwright` 和 `pytest-playwright`。
        *   測試腳本應盡量模擬真實使用者的操作流程，例如：
            1.  打開瀏覽器，訪問 Web UI。
            2.  在輸入框中輸入資料夾路徑。
            3.  點擊「處理」按鈕。
            4.  輪詢 API 或檢查頁面元素，直到看到「處理完成」的狀態。
            5.  驗證頁面上顯示的結果是否符合預期。
    *   **範例 (`tests/e2e/test_full_eqc_workflow.py`):** 
        ```python
        def test_full_eqc_workflow(page):
            page.goto("http://localhost:8010/ui")
            page.fill("#folderPath", "D:\\test_data\\eqc_case_1")
            page.click("button#process-btn")

            # 等待處理完成的標誌出現，最長等待 60 秒
            success_element = page.locator("#status-success")
            success_element.wait_for(timeout=60000)

            # 斷言結果是否正確
            result_text = page.locator("#result-summary").inner_text()
            assert "良率: 99.5%" in result_text
        ```

#### **Rationale (決策理由)**

*   **分層測試:** 採用測試金字塔模型，是因為不同層級的測試有不同的成本和收益。單元測試運行速度快、穩定、能精確定位問題，因此數量應該最多。E2E 測試運行慢、不穩定（易受網路、環境影響），但最接近真實使用者場景，因此數量應較少，只覆蓋最關鍵的業務流程。
*   **自動化:** 整套測試策略的設計都考慮了自動化。所有測試都應該可以透過 `make test` 或 `pytest` 一鍵執行，並整合到 CI/CD 管線中，在每次提交程式碼時自動運行。
*   **隔離性:** 強調了測試之間的隔離，特別是整合測試應使用獨立的資料庫，確保測試的可重複性，避免測試之間互相干擾。

這套全面的測試策略，是確保這個複雜的多服務系統能夠在持續的迭代和重構中，依然保持高品質和穩定性的重要保障。

## 17. 編碼標準 (Coding Standards)

為了確保 AI 代理和人類開發者都能產出風格一致、高品質的程式碼，所有貢獻者都應遵守以下核心標準。

#### **1. 核心規則 (Critical Fullstack Rules)**

*   **類型提示 (Type Hinting):** 
    *   **規則:** **所有**函式的參數和返回值都**必須**有明確的類型提示。
    *   **理由:** 類型提示是現代 Python 的基石。它能讓靜態分析工具（如 Mypy）在執行前就發現潛在的類型錯誤，並極大地增強了程式碼的可讀性和 IDE 的自動完成能力。
    *   **範例:** 
        ```python
        # Good
        def process_data(data: list[dict[str, any]]) -> bool:
            # ...
            return True

        # Bad
        def process_data(data):
            # ...
            return True
        ```

*   **服務層抽象 (Service Layer Abstraction):** 
    *   **規則:** **絕不**在 API 端點（`ft_eqc_api.py`, `email_inbox_app.py`）中直接編寫複雜的業務邏輯或資料庫查詢。所有邏輯都必須封裝在 `services` 層的類別中。
    *   **理由:** 這強制實現了關注點分離。API 層只負責處理 HTTP 請求和回應，而業務邏輯則被清晰地組織在可重用的服務中，便於獨立測試和維護。

*   **設定檔驅動 (Configuration Driven):** 
    *   **規則:** **絕不**在程式碼中硬編碼任何設定值（例如，URL、檔案路徑、API 金鑰、埠號）。所有設定值都必須從設定檔或環境變數中讀取。
    *   **理由:** 這使得應用程式可以輕鬆地在不同環境（開發、測試、生產）之間遷移，而無需修改任何程式碼。

*   **結構化日誌 (Structured Logging):** 
    *   **規則:** 使用 `loguru` 或 `structlog` 記錄帶有上下文的結構化日誌，而不是使用簡單的 `print()` 或 `logging.info()`。
    *   **理由:** 結構化日誌可以被日誌分析平台（如 ELK, Grafana Loki）輕鬆地解析、索引和查詢。在除錯分散式系統時，能夠根據 `trace_id` 或 `user_id` 篩選日誌是至關重要的。
    *   **範例:** 
        ```python
        # Good
        logger.bind(user_id=123, task_id="abc").info("Task started")

        # Bad
        print(f"User 123 started task abc")
        ```

*   **檔案長度限制 (File Length Limit):** 
    *   **規則:** 盡力將每個 Python 檔案 (`.py`) 的長度保持在 **500 行以內**。這不是一個絕對的硬性規定，而是一個強烈的**指導原則**。
    *   **例外:** 
        *   自動生成的檔案（例如，某些 `models.py` 或 `enums.py`）可以不受此限制。
        *   如果一個類別本身非常複雜，且拆分到多個檔案會降低而不是提高可讀性，可以在程式碼審查 (Code Review) 中提出討論，並作為特例被接受。
    *   **理由:** 
        1.  **促進單一職責原則 (Promotes Single Responsibility):** 如果一個檔案超過了 500 行，這通常是一個強烈的「訊號」，表明這個檔案可能承擔了太多的職責。它迫使開發者停下來思考：「這個檔案裡的類別或函式，是否可以被拆分成更小、更專注的模組？」
        2.  **提高可讀性與可維護性:** 小檔案更容易被完整地讀取和理解。當一個開發者打開一個小檔案時，他可以更快地將整個檔案的上下文載入到自己的大腦中，從而更容易地進行修改和除錯。沒有人喜歡在一個幾千行的檔案中滾動來滾去找東西。
        3.  **降低認知負荷 (Reduces Cognitive Load):** 一個 500 行的檔案通常只包含少數幾個關聯緊密的類別或函式，這使得理解它們之間的關係變得更容易。
        4.  **便於程式碼審查 (Easier Code Reviews):** 審查一個 200 行的變更，遠比審查一個 2000 行的變更要輕鬆和有效得多。審查者可以更仔細地檢查程式碼的邏輯細節。

#### **2. 命名慣例 (Naming Conventions)**

| 元素 | 命名風格 | 範例 |
| :--- | :--- | :--- |
| **變數** | `snake_case` (小寫蛇形) | `processing_time` |
| **函式** | `snake_case` (小寫蛇形) | `calculate_yield()` |
| **類別** | `PascalCase` (大駝峰) | `EQCProcessingService` |
| **常數** | `UPPER_SNAKE_CASE` (大寫蛇形) | `DEFAULT_TIMEOUT = 60` |
| **模組/檔案** | `snake_case` (小寫蛇形) | `eqc_session_manager.py` |
| **類別方法** | `snake_case` (小寫蛇形) | `def run_analysis(self):` |
| **API 端點** | `snake_case` (小寫蛇形) | `@app.post("/process_eqc_advanced")` |
| **資料庫表** | `snake_case` (小寫蛇形) | `email_process_status` |

#### **3. 自動化工具**

為了強制執行以上標準，我們將依賴以下自動化工具，並將它們整合到 CI/CD 管線中：

*   **程式碼格式化:** **Ruff Formatter** (或 Black) - 自動統一所有程式碼的格式。
*   **靜態分析/Linter:** **Ruff** - 檢查程式碼中的潛在錯誤、不好的實踐和風格問題。
*   **類型檢查:** **Mypy** - 嚴格檢查程式碼中的類型提示是否正確。

#### **Rationale (決策理由)**

*   **少即是多:** 我沒有列出幾十條瑣碎的規則，而是選擇了幾個最能影響程式碼庫長期健康的**高槓桿**規則。
*   **與架構一致:** 這些規則（特別是服務層抽象和設定檔驅動）是對我們之前討論的架構設計原則的具體落實。
*   **自動化強制:** 最好的標準是能夠被工具自動執行的標準。依賴 Ruff 和 Mypy，可以將風格和正確性的檢查工作從「人」的審查中解放出來，變得客觀而高效。
*   **遵循社群:** 命名慣例完全遵循 PEP 8，這是 Python 社群最廣泛接受的風格指南，降低了新成員的學習成本。

這套編碼標準為團隊提供了一套共同的語言，有助於寫出每個人都容易閱讀、理解和維護的程式碼。

## 18. 錯誤處理策略 (Error Handling Strategy)

#### **1. 錯誤流程 (Error Flow)**

以下序列圖展示了一個統一的錯誤處理流程，從錯誤的發生到最終的處理和記錄。

```mermaid
sequenceDiagram
    participant Client as 客戶端 (前端/其他服務)
    participant API_Gateway as API 閘道 (Nginx/Traefik)
    participant Service as 後端服務 (Flask/FastAPI)
    participant Business_Logic as 業務邏輯層
    participant Infrastructure as 基礎設施層 (DB/外部API)
    participant Logger as 日誌系統 (Loguru/Structlog)
    participant Monitoring as 監控系統 (Prometheus/Grafana)
    participant Alerting as 警報系統 (LINE/Email)

    Client->>API_Gateway: 1. 發送請求
    API_Gateway->>Service: 2. 轉發請求

    Service->>Business_Logic: 3. 呼叫業務邏輯
    Business_Logic->>Infrastructure: 4. 呼叫基礎設施 (可能發生錯誤)

    alt 錯誤發生
        Infrastructure--xBusiness_Logic: 5. 拋出異常 (e.g., DatabaseError, NetworkError)
        Business_Logic--xService: 6. 異常傳播 (e.g., ValueError, CustomError)
        Service->>Logger: 7. 記錄詳細錯誤日誌 (包含上下文)
        Service->>Monitoring: 8. 增加錯誤計數指標
        Service->>API_Gateway: 9. 返回標準化錯誤回應 (HTTP 狀態碼 + 錯誤訊息)
        API_Gateway->>Client: 10. 轉發錯誤回應

        Service->>Alerting: 11. (如果錯誤嚴重) 發送警報通知
    else 正常流程
        Infrastructure-->>Business_Logic: 5. 返回結果
        Business_Logic-->>Service: 6. 返回結果
        Service-->>API_Gateway: 7. 返回成功回應
        API_Gateway-->>Client: 8. 轉發成功回應
    end
```

#### **2. 錯誤回應格式 (Error Response Format)**

所有對外暴露的 API 都應返回統一的錯誤回應格式。這使得客戶端能夠一致地處理來自不同服務的錯誤。

```typescript
interface ApiError {
  error: {
    code: string;          // 錯誤代碼 (例如: "INVALID_INPUT", "RESOURCE_NOT_FOUND", "INTERNAL_SERVER_ERROR")
    message: string;       // 錯誤訊息 (給開發者或技術支援人員看)
    details?: Record<string, any>; // 錯誤的詳細資訊 (例如: 哪個欄位驗證失敗)
    timestamp: string;     // 錯誤發生時間 (ISO 8601)
    requestId?: string;    // 請求的唯一 ID (用於追蹤日誌)
  };
}
```

#### **3. 前端錯誤處理 (Frontend Error Handling)**

*   **策略:** 友善地向使用者展示錯誤，並提供可行的解決方案或引導。
*   **實施:** 
    1.  **統一攔截:** 在 API 客戶端層（例如 `api-client.js`）統一攔截所有 API 錯誤回應。
    2.  **錯誤訊息展示:** 根據 `ApiError` 格式，提取 `message` 和 `details`，並以使用者友善的方式（例如，Toast 提示、模態框）顯示給使用者。
    3.  **使用者引導:** 對於可恢復的錯誤（如輸入驗證失敗），引導使用者修正輸入。對於不可恢復的錯誤（如內部伺服器錯誤），提示使用者稍後重試，並提供技術支援的聯絡方式或 `requestId`。
    4.  **日誌記錄:** 在瀏覽器控制台記錄詳細錯誤，方便前端開發者除錯。

#### **4. 後端錯誤處理 (Backend Error Handling)**

*   **策略:** 捕獲所有預期和非預期錯誤，進行標準化處理、日誌記錄和通知。
*   **實施:** 
    1.  **自訂異常 (Custom Exceptions):** 為業務邏輯中可能發生的預期錯誤定義自訂異常（例如 `ResourceNotFound`, `InvalidInput`, `PermissionDenied`）。這使得錯誤的類型更清晰。
    2.  **全局異常處理器 (Global Exception Handlers):** 
        *   **FastAPI:** 利用 `@app.exception_handler` 捕獲所有未處理的異常，並將其轉換為標準的 `ApiError` 格式回應。
        *   **Flask:** 使用 `@app.errorhandler` 捕獲 HTTP 錯誤和自訂異常。
    3.  **日誌記錄:** 
        *   使用 `loguru` 或 `structlog` 記錄所有錯誤，並包含足夠的上下文資訊（如 `requestId`, `user_id`, 請求參數）。
        *   對於嚴重錯誤（例如 5xx 錯誤），應記錄為 `ERROR` 級別。
    4.  **錯誤通知:** 
        *   整合監控系統（如 Prometheus）來收集錯誤指標。
        *   對於生產環境中的關鍵錯誤，應觸發警報系統（如 LINE 通知、Email）通知相關負責人。
    5.  **避免洩露敏感資訊:** 錯誤回應中**絕不能**包含堆疊追蹤 (Stack Trace) 或其他敏感的系統內部資訊。

#### **Rationale (決策理由)**

*   **統一性:** 確保所有錯誤都以一致的方式被處理和呈現，無論錯誤發生在哪個服務或哪個層級。
*   **可追溯性:** 透過 `requestId` 和結構化日誌，可以輕鬆地追蹤一個錯誤從客戶端到後端的完整生命週期，極大地簡化了除錯過程。
*   **使用者體驗:** 友善的錯誤訊息和引導，可以減少使用者的挫敗感。
*   **系統穩定性:** 完善的錯誤捕獲和通知機制，可以幫助團隊快速響應和解決問題，提高系統的可用性。

這套錯誤處理策略旨在將錯誤從「意外」轉變為「可管理」的事件，從而提升系統的整體健壯性。

## 20. 檢查清單結果報告 (Checklist Results Report)

本報告記錄了在 **Outlook Summary System 全端架構文件** 設計過程中，針對關鍵架構決策點的評估結果。

| 檢查項目 | 評估結果 | 備註 |
| :--- | :--- | :--- |
| **1. 專案類型確認** | **棕地專案** | 基於現有程式碼庫進行演進，而非全新開發。 |
| **2. 後端框架選型** | **Flask + FastAPI (雙服務)** | Flask 負責 Web UI，FastAPI 負責高效能 API。 |
| **3. 非同步處理機制** | **Dramatiq + Redis** | 確保耗時任務的背景執行與 API 響應速度。 |
| **4. 資料庫選型** | **SQLite (開發) / PostgreSQL (生產)** | 關聯式資料庫，透過 SQLAlchemy ORM 管理。 |
| **5. 前端架構類型** | **混合式 (SSR + SPA)** | Flask 負責傳統 Web UI，FastAPI 提供獨立 SPA。 |
| **6. 認證與授權狀態** | **程式碼內建，預設關閉** | 存在 `enable_authentication` 開關，需手動啟用。 |
| **7. 專案結構清晰度** | **已優化分層結構** | 建議採用 `src/presentation`, `src/services`, `src/infrastructure` 等分層。 |
| **8. 本地開發環境** | **Docker Compose (推薦)** | 透過卷掛載實現熱重載，確保環境一致性。 |
| **9. 部署環境類型** | **內網環境** | 部署策略基於 Docker Compose，不依賴公網。 |
| **10. 錯誤處理標準化** | **統一錯誤回應格式** | 確保客戶端一致處理錯誤，提升可追溯性。 |
| **11. 監控與可觀測性** | **Prometheus + Grafana (基礎)** | 建議引入 Loki/Jaeger 完善日誌與追蹤。 |
| **12. 測試策略完整性** | **測試金字塔 (單元/整合/E2E)** | Pytest + Playwright，確保功能正確性。 |
| **13. 編碼標準執行** | **已定義關鍵規則** | 包含類型提示、服務層抽象、檔案長度限制 (500 行) 等。 |
| **14. 程式碼複雜度考量** | **引入循環複雜度** | 建議在 CI/CD 中強制執行，輔助行數限制。 |

#### **Rationale (決策理由)**

這個檢查清單是對我們之前所有討論的一個**總結和確認**。它以表格的形式，清晰地列出了每個關鍵架構決策的結果，並提供了簡潔的備註。這有助於快速回顧整個設計過程，並確保所有重要的方面都已得到考慮。

## 21. 最終審閱與簽核 (Final Review and Sign-off)

這份文件現已完成。請仔細審閱所有章節。

一旦獲得批准，這份架構文件將成為本專案所有開發工作的最終指南。未來任何變更都必須經過批准並在此處進行記錄。

**請提供您的最終簽核或任何剩餘的回饋。**

**批准人：**
*   姓名：
*   職位：
*   日期：

**下一步：**
*   `dev` 代理將使用這份文件來實施專案。
*   `po` 代理將使用這份文件來驗證故事與架構的一致性。
*   `qa` 代理將使用這份文件來建立測試計畫。

感謝您的協作。