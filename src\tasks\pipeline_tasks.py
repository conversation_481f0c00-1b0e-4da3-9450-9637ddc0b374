"""
Dramatiq 任務管道系統 - 核心管道任務實作

🎯 功能：
  - 實作 vendor files 處理管道
  - 支援重試機制和監控
  - 使用正確的 Dramatiq 回調機制
  - 集成現有的 FileHandlerFactory 和 VendorFileMonitor
  - 標準化返回格式供下游任務使用

🔧 技術要求：
  - 使用回調機制串聯任務
  - 保持最小侵入原則
  - 添加完整的錯誤處理和日誌記錄
  - 支援配置開關以支援 A/B 測試
"""

import asyncio
import traceback
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

import dramatiq
from dramatiq import actor
from loguru import logger

# 導入現有基礎設施
from src.infrastructure.adapters.file_handlers.file_handler_factory import FileHandlerFactory
from src.services.vendor_file_monitor import get_vendor_file_monitor, FileProcessingStatus
from src.services.vendor_file_notification import get_vendor_file_notification_service
from src.tasks.retry_tracker import get_retry_tracker, send_failure_notification_if_needed


# ============================================================================
# 🔧 重試邏輯輔助函數
# ============================================================================

def _should_retry_with_logging(retries_so_far: int, exception: Exception, max_retries: int = 3) -> bool:
    """
    判斷是否應該重試，並記錄詳細日誌
    
    Args:
        retries_so_far: 已經重試的次數
        exception: 發生的異常
        max_retries: 最大重試次數
        
    Returns:
        bool: 是否應該重試
    """
    # 不可重試的異常類型
    non_retriable_exceptions = (ValueError, FileNotFoundError)
    is_retriable_error = not isinstance(exception, non_retriable_exceptions)
    
    should_retry = retries_so_far < max_retries and is_retriable_error
    
    logger.info(
        f"[RETRY_LOGIC] 重試決策: retries={retries_so_far}/{max_retries}, "
        f"exception={type(exception).__name__}, retriable={is_retriable_error}, "
        f"will_retry={should_retry}"
    )
    
    # 記錄重試決策但不在此處發送通知
    # 通知發送應該由 actor 的 on_failure 回調處理
    if not should_retry:
        if not is_retriable_error:
            logger.info(f"[RETRY_LOGIC] 不可重試異常: {type(exception).__name__} - 將觸發 LINE 通知")
        else:
            logger.info(f"[RETRY_LOGIC] 已達最大重試次數: {retries_so_far}/{max_retries} - 將觸發 LINE 通知")
    elif should_retry:
        logger.info(f"[RETRY_LOGIC] 將進行第 {retries_so_far + 1} 次重試")
    
    return should_retry


def _send_final_failure_notification(
    exception: Exception,
    retry_count: int,
    vendor_code: str,
    mo: str,
    temp_path: str,
    pd: str,
    lot: str,
    email_subject: str,
    email_body: str,
    pipeline_context: Optional[Dict[str, Any]],
    task_id: str
) -> None:
    """
    在最終失敗時發送 LINE 通知
    直接使用傳入的任務參數
    """
    try:
        logger.info(f"[FINAL_NOTIFICATION] 準備發送最終失敗通知: {vendor_code}/{mo}")
        
        # 從 pipeline_context 中獲取 tracking_id，如果沒有則使用 task_id
        tracking_id = pipeline_context.get('tracking_id', task_id) if pipeline_context else task_id

        # 發送通知
        success = send_failure_notification_if_needed(
            task_id=task_id,
            vendor_code=vendor_code,
            mo=mo,
            temp_path=temp_path,
            pd=pd,
            lot=lot,
            error_message=f"廠商檔案處理失敗: {str(exception)}",
            exception=exception,
            email_subject=email_subject,
            email_body=email_body,
            tracking_id=tracking_id,
            processing_time=0.0,  # 無法從此處獲取準確的處理時間
            retry_count=retry_count
        )
        
        if success:
            logger.info(f"[FINAL_NOTIFICATION] ✅ 最終通知發送完成: {vendor_code}/{mo}")
        else:
            logger.error(f"[FINAL_NOTIFICATION] ❌ 最終通知發送失敗: {vendor_code}/{mo}")
            
    except Exception as e:
        logger.error(f"[FINAL_NOTIFICATION] 發送最終通知時出錯: {e}")


def _get_retry_info_from_message() -> tuple[int, bool]:
    """
    從 Dramatiq 消息中獲取重試資訊
    
    Returns:
        tuple[int, bool]: (當前重試次數, 是否成功獲取)
    """
    try:
        current_message = dramatiq.get_current_message()
        if not current_message:
            return 0, False
        
        # 嘗試多種方法獲取重試計數
        retry_count = 0
        detection_method = "unknown"
        
        # 方法1: 檢查 retried_count 屬性
        if hasattr(current_message, 'retried_count'):
            retry_count = current_message.retried_count
            detection_method = "retried_count_attr"
        # 方法2: 檢查 options 中的 retried_count
        elif 'retried_count' in current_message.options:
            retry_count = current_message.options.get('retried_count', 0)
            detection_method = "options_retried_count"
        # 方法3: 檢查 options 中的 retries
        elif 'retries' in current_message.options:
            retry_count = current_message.options.get('retries', 0)
            detection_method = "options_retries"
        # 方法4: 嘗試從消息ID推斷（啟發式）
        else:
            message_id = str(getattr(current_message, 'message_id', ''))
            # Dramatiq 在重試時可能會修改 message_id
            if 'retry' in message_id.lower():
                retry_count = 1  # 保守估計
                detection_method = "message_id_heuristic"
        
        logger.debug(
            f"[RETRY_DETECTION] 方法={detection_method}, 重試次數={retry_count}, "
            f"message_id={current_message.message_id}, options={current_message.options}"
        )
        
        return retry_count, True
        
    except Exception as e:
        logger.warning(f"[RETRY_DETECTION] 無法獲取重試資訊: {e}")
        return 0, False

# 確保 Dramatiq 配置已載入
try:
    import dramatiq_config  # 這會觸發配置初始化
    logger.info("✅ Dramatiq 配置已載入，包含 Results middleware")
except ImportError as e:
    logger.warning(f"⚠️  無法載入 dramatiq_config: {e}")


# ============================================================================
# 🎯 核心管道任務
# ============================================================================

@actor(
    queue_name="pipeline_queue",
    max_retries=3,
    time_limit=600000,  # 10分鐘
    store_results=True,  # 啟用結果存儲 - 需要 Results middleware
    # 🔧 修復重試機制: 使用簡單的重試邏輯，避免參數作用域問題
    retry_when=lambda retries_so_far, exception: (
        retries_so_far < 3 and not isinstance(exception, (ValueError, FileNotFoundError))
    )
)
async def process_vendor_files_task(
    vendor_code: str,
    mo: str,
    temp_path: str,
    pd: str = "default",
    lot: str = "default",
    email_subject: str = "",
    email_body: str = "",
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    處理廠商檔案任務 - 支援重試機制和監控
    
    Args:
        vendor_code: 廠商代碼
        mo: MO 編號
        temp_path: 暫存路徑
        pd: 產品名稱
        lot: 批號
        email_subject: 郵件主旨
        email_body: 郵件內文
        pipeline_context: 管道上下文資訊
        
    Returns:
        Dict[str, Any]: 標準化的處理結果
    """
    import time
    start_time = time.time()
    
    # 獲取任務ID
    task_id = None
    try:
        current_message = dramatiq.get_current_message()
        if current_message:
            task_id = current_message.message_id
    except Exception:
        import uuid
        task_id = str(uuid.uuid4())
    
    logger.info(f"[PIPELINE] 開始處理廠商檔案: vendor={vendor_code}, mo={mo}, task_id={task_id}")
    
    # 初始化監控
    monitor = get_vendor_file_monitor()
    tracking_id = monitor.start_tracking(
        file_path=temp_path,
        vendor_name=vendor_code,
        expected_size=None
    )
    
    try:
        # Step 1: 更新進度 - 開始處理
        monitor.update_progress(tracking_id, FileProcessingStatus.STARTED, 10, "初始化廠商檔案處理")
        
        # Step 2: 建立檔案處理器
        factory = FileHandlerFactory()
        monitor.update_progress(tracking_id, FileProcessingStatus.PROCESSING, 25, "建立檔案處理器")
        
        # Step 3: 執行檔案處理
        monitor.update_progress(tracking_id, FileProcessingStatus.PROCESSING, 50, "執行檔案複製")
        
        result = factory.process_vendor_files(
            vendor_code=vendor_code,
            mo=mo,
            temp_path=temp_path,
            pd=pd,
            lot=lot,
            email_subject=email_subject,
            email_body=email_body
        )
        
        # Step 4: 檢查處理結果
        if result is None:
            error_msg = "檔案處理器返回 None 結果"
            logger.error(f"[PIPELINE] {error_msg}: vendor={vendor_code}, mo={mo}")
            monitor.record_error(tracking_id, error_msg, "processing")
            raise Exception(error_msg)
        elif not result.get('success', False):
            error_msg = result.get('error', '檔案處理失敗')
            logger.error(f"[PIPELINE] 檔案處理失敗: {error_msg}, vendor={vendor_code}, mo={mo}")
            monitor.record_error(tracking_id, error_msg, "processing")
            raise Exception(error_msg)
        
        # Step 5: 計算處理時間和完成
        processing_time = time.time() - start_time
        monitor.update_progress(tracking_id, FileProcessingStatus.COMPLETED, 100, "檔案處理完成")
        monitor.complete_tracking(tracking_id, success=True)
        
        # 標準化返回格式
        standardized_result = {
            'task_type': 'process_vendor_files',
            'task_id': task_id,
            'tracking_id': tracking_id,
            'status': 'completed',
            'success': True,
            'vendor_code': vendor_code,
            'mo': mo,
            'temp_path': temp_path,
            'pd': pd,
            'lot': lot,
            'processing_time': processing_time,
            'completed_at': datetime.now().isoformat(),
            'vendor_result': result,
            'pipeline_context': pipeline_context or {},
            'next_task_params': {
                # 為下游任務準備參數
                'input_path': temp_path,
                'vendor_code': vendor_code,
                'mo': mo,
                'processing_result': result
            }
        }
        
        logger.info(f"[PIPELINE] 廠商檔案處理完成: vendor={vendor_code}, mo={mo}, 耗時={processing_time:.2f}s")
        return standardized_result
        
    except FileNotFoundError as e:
        error_msg = f"廠商檔案處理失敗 - 檔案未找到: {str(e)}"
        logger.error(f"[PIPELINE] {error_msg}")
        
        monitor.record_error(tracking_id, error_msg, "io")
        monitor.complete_tracking(tracking_id, success=False)
        
        # 發送 LINE 通知 - FileNotFoundError 為最終失敗
        try:
            notification_service = get_vendor_file_notification_service()
            notification_service.notify_vendor_file_processing_failure(
                vendor_code=vendor_code,
                mo=mo,
                temp_path=temp_path,
                pd=pd,
                lot=lot,
                error_message=error_msg,
                email_subject=email_subject,
                email_body=email_body,
                task_id=task_id,
                tracking_id=tracking_id,
                processing_time=time.time() - start_time,
                retry_count=0  # FileNotFoundError 不重試
            )
        except Exception as notify_error:
            logger.warning(f"[PIPELINE] 發送失敗通知時出錯: {notify_error}")
        
        return {
            'task_type': 'process_vendor_files',
            'task_id': task_id,
            'tracking_id': tracking_id,
            'status': 'failed',
            'success': False,
            'vendor_code': vendor_code,
            'mo': mo,
            'error': error_msg,
            'error_type': 'FileNotFoundError',
            'processing_time': time.time() - start_time,
            'failed_at': datetime.now().isoformat(),
            'retryable': False,
            'pipeline_context': pipeline_context or {}
        }
        
    except ValueError as e:
        error_msg = f"廠商檔案處理失敗 - 參數錯誤: {str(e)}"
        logger.error(f"[PIPELINE] {error_msg}")
        
        monitor.record_error(tracking_id, error_msg, "validation")
        monitor.complete_tracking(tracking_id, success=False)
        
        # 發送 LINE 通知 - ValueError 為最終失敗
        try:
            notification_service = get_vendor_file_notification_service()
            notification_service.notify_vendor_file_processing_failure(
                vendor_code=vendor_code,
                mo=mo,
                temp_path=temp_path,
                pd=pd,
                lot=lot,
                error_message=error_msg,
                email_subject=email_subject,
                email_body=email_body,
                task_id=task_id,
                tracking_id=tracking_id,
                processing_time=time.time() - start_time,
                retry_count=0  # ValueError 不重試
            )
        except Exception as notify_error:
            logger.warning(f"[PIPELINE] 發送失敗通知時出錯: {notify_error}")
        
        return {
            'task_type': 'process_vendor_files',
            'task_id': task_id,
            'tracking_id': tracking_id,
            'status': 'failed',
            'success': False,
            'vendor_code': vendor_code,
            'mo': mo,
            'error': error_msg,
            'error_type': 'ValueError',
            'processing_time': time.time() - start_time,
            'failed_at': datetime.now().isoformat(),
            'retryable': False,
            'pipeline_context': pipeline_context or {}
        }
        
    except Exception as e:
        error_msg = f"廠商檔案處理失敗: {str(e)}"
        logger.error(f"[PIPELINE] {error_msg}\n{traceback.format_exc()}")
        
        monitor.record_error(tracking_id, error_msg, "general")
        monitor.complete_tracking(tracking_id, success=False)
        
        # 🔥 簡化重試邏輯 - 讓 retry_tracker.py 統一處理所有重試決策
        logger.info(f"[PIPELINE] ❌ 任務執行失敗: {vendor_code}/{mo}, 錯誤: {error_msg}")
        
        # 使用統一的通知處理，所有重試邏輯由 retry_tracker 決定
        notification_sent = send_failure_notification_if_needed(
            task_id=task_id,
            vendor_code=vendor_code,
            mo=mo,
            temp_path=temp_path,
            pd=pd,
            lot=lot,
            error_message=error_msg,
            exception=e,
            email_subject=email_subject,
            email_body=email_body,
            tracking_id=tracking_id,
            processing_time=time.time() - start_time
        )
        
        if notification_sent:
            logger.info(f"[PIPELINE] 📱 最終失敗通知已發送: {vendor_code}/{mo}")
        else:
            logger.info(f"[PIPELINE] ⏳ 任務可重試，暫不發送通知: {vendor_code}/{mo}")
        
        # 直接重新拋出錯誤，讓 Dramatiq 處理重試
        raise
        
        return {
            'task_type': 'process_vendor_files',
            'task_id': task_id,
            'tracking_id': tracking_id,
            'status': 'failed',
            'success': False,
            'vendor_code': vendor_code,
            'mo': mo,
            'error': error_msg,
            'error_type': type(e).__name__,
            'processing_time': time.time() - start_time,
            'failed_at': datetime.now().isoformat(),
            'notification_sent': notification_sent,
            'retry_tracker_used': True,
            'pipeline_context': pipeline_context or {}
        }


@actor(
    queue_name="pipeline_queue",
    max_retries=2,
    time_limit=600000,
    store_results=True  # 需要 Results middleware 支持
)
async def pipeline_completion_task(
    pipeline_id: str,
    all_results: List[Dict[str, Any]],
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    管道完成任務 - 彙總所有步驟的結果
    
    Args:
        pipeline_id: 管道ID
        all_results: 所有步驟的結果列表
        pipeline_context: 管道上下文
        
    Returns:
        Dict[str, Any]: 管道完成結果
    """
    import time
    start_time = time.time()
    
    logger.info(f"[PIPELINE] 開始管道完成任務: pipeline_id={pipeline_id}")
    
    try:
        # 分析所有結果
        successful_tasks = [r for r in all_results if r.get('success', False)]
        failed_tasks = [r for r in all_results if not r.get('success', False)]
        
        total_processing_time = sum(r.get('processing_time', 0) for r in all_results)
        completion_time = time.time() - start_time
        
        # 創建最終結果
        final_result = {
            'pipeline_id': pipeline_id,
            'pipeline_type': 'vendor_processing',
            'status': 'completed' if len(failed_tasks) == 0 else 'partially_failed',
            'success': len(failed_tasks) == 0,
            'total_tasks': len(all_results),
            'successful_tasks': len(successful_tasks),
            'failed_tasks': len(failed_tasks),
            'total_processing_time': total_processing_time,
            'completion_time': completion_time,
            'completed_at': datetime.now().isoformat(),
            'task_results': all_results,
            'pipeline_context': pipeline_context or {},
            'summary': {
                'vendors_processed': list(set(r.get('vendor_code') for r in all_results if r.get('vendor_code'))),
                'mos_processed': list(set(r.get('mo') for r in all_results if r.get('mo'))),
                'errors': [r.get('error') for r in failed_tasks if r.get('error')]
            }
        }
        
        logger.info(f"[PIPELINE] 管道完成: pipeline_id={pipeline_id}, "
                   f"成功={len(successful_tasks)}, 失敗={len(failed_tasks)}")
        
        return final_result
        
    except Exception as e:
        error_msg = f"管道完成任務失敗: {str(e)}"
        logger.error(f"[PIPELINE] {error_msg}\n{traceback.format_exc()}")
        
        return {
            'pipeline_id': pipeline_id,
            'pipeline_type': 'vendor_processing',
            'status': 'failed',
            'success': False,
            'error': error_msg,
            'completion_time': time.time() - start_time,
            'failed_at': datetime.now().isoformat(),
            'task_results': all_results,
            'pipeline_context': pipeline_context or {}
        }


# ============================================================================
# 🔧 管道編排函數
# ============================================================================

def create_vendor_processing_pipeline(
    vendor_files: List[Dict[str, Any]],
    pipeline_context: Optional[Dict[str, Any]] = None
) -> str:
    """
    創建廠商處理管道 - 使用回調機制串聯任務
    
    Args:
        vendor_files: 廠商檔案列表，每個包含 vendor_code, mo, temp_path 等
        pipeline_context: 管道上下文
        
    Returns:
        str: 管道ID
        
    Example:
        vendor_files = [
            {
                'vendor_code': 'GTK',
                'mo': 'MO12345',
                'temp_path': '/tmp/file1.zip',
                'pd': 'product1',
                'lot': 'lot1',
                'email_subject': 'subject',
                'email_body': 'body'
            }
        ]
        pipeline_id = create_vendor_processing_pipeline(vendor_files)
    """
    import uuid
    pipeline_id = str(uuid.uuid4())
    
    logger.info(f"[PIPELINE] 創建廠商處理管道: pipeline_id={pipeline_id}, files_count={len(vendor_files)}")
    
    # 為每個廠商檔案創建處理任務
    task_messages = []
    
    for i, vendor_file in enumerate(vendor_files):
        # 添加管道上下文
        file_pipeline_context = {
            'pipeline_id': pipeline_id,
            'task_index': i,
            'total_tasks': len(vendor_files),
            **(pipeline_context or {})
        }
        
        # 創建任務訊息
        message = process_vendor_files_task.message(
            vendor_code=vendor_file.get('vendor_code'),
            mo=vendor_file.get('mo'),
            temp_path=vendor_file.get('temp_path'),
            pd=vendor_file.get('pd', 'default'),
            lot=vendor_file.get('lot', 'default'),
            email_subject=vendor_file.get('email_subject', ''),
            email_body=vendor_file.get('email_body', ''),
            pipeline_context=file_pipeline_context
        )
        
        task_messages.append(message)
    
    # 使用 group 並行執行所有任務
    from dramatiq import group
    task_group = group(task_messages)
    
    # 發送任務組
    task_group.run()
    
    logger.info(f"[PIPELINE] 管道已啟動: pipeline_id={pipeline_id}")
    return pipeline_id


def create_sequential_vendor_pipeline(
    vendor_files: List[Dict[str, Any]],
    pipeline_context: Optional[Dict[str, Any]] = None
) -> str:
    """
    創建序列化廠商處理管道 - 一個接一個執行
    
    Args:
        vendor_files: 廠商檔案列表
        pipeline_context: 管道上下文
        
    Returns:
        str: 管道ID
    """
    import uuid
    pipeline_id = str(uuid.uuid4())
    
    logger.info(f"[PIPELINE] 創建序列化廠商處理管道: pipeline_id={pipeline_id}, files_count={len(vendor_files)}")
    
    # 建立序列化任務鏈
    current_message = None
    
    for i, vendor_file in enumerate(vendor_files):
        file_pipeline_context = {
            'pipeline_id': pipeline_id,
            'task_index': i,
            'total_tasks': len(vendor_files),
            'sequential': True,
            **(pipeline_context or {})
        }
        
        message = process_vendor_files_task.message(
            vendor_code=vendor_file.get('vendor_code'),
            mo=vendor_file.get('mo'),
            temp_path=vendor_file.get('temp_path'),
            pd=vendor_file.get('pd', 'default'),
            lot=vendor_file.get('lot', 'default'),
            email_subject=vendor_file.get('email_subject', ''),
            email_body=vendor_file.get('email_body', ''),
            pipeline_context=file_pipeline_context
        )
        
        if current_message is None:
            # 第一個任務
            current_message = message
        else:
            # 使用回調鏈接下一個任務
            # 注意：Dramatiq 不直接支援管道操作符，使用替代方案
            pass
    
    # 發送第一個任務
    if current_message:
        current_message.send()
    
    logger.info(f"[PIPELINE] 序列化管道已啟動: pipeline_id={pipeline_id}")
    return pipeline_id


# ============================================================================
# 🔍 管道狀態查詢和工具
# ============================================================================

def get_pipeline_status(pipeline_id: str) -> Dict[str, Any]:
    """
    查詢管道狀態
    
    Args:
        pipeline_id: 管道ID
        
    Returns:
        Dict[str, Any]: 管道狀態信息
    """
    monitor = get_vendor_file_monitor()
    
    # 查找與該管道相關的跟踪記錄
    active_trackings = monitor.get_active_trackings()
    pipeline_trackings = [
        t for t in active_trackings 
        if t.tracking_id and pipeline_id in str(t.tracking_id)  # 簡化的匹配邏輯
    ]
    
    return {
        'pipeline_id': pipeline_id,
        'active_tasks': len(pipeline_trackings),
        'task_statuses': [
            {
                'tracking_id': t.tracking_id,
                'status': t.status.value,
                'progress': t.progress_percentage,
                'file_name': t.file_name,
                'vendor_name': t.vendor_name
            }
            for t in pipeline_trackings
        ],
        'last_updated': datetime.now().isoformat()
    }


def cancel_pipeline(pipeline_id: str) -> Dict[str, Any]:
    """
    取消管道執行（盡力而為）
    
    Args:
        pipeline_id: 管道ID
        
    Returns:
        Dict[str, Any]: 取消結果
    """
    logger.warning(f"[PIPELINE] 嘗試取消管道: pipeline_id={pipeline_id}")
    
    # 注意：Dramatiq 沒有內建的任務取消機制
    # 這裡只能記錄取消請求，實際的任務可能仍會執行
    
    return {
        'pipeline_id': pipeline_id,
        'cancel_requested': True,
        'message': 'Pipeline cancellation requested (best effort)',
        'timestamp': datetime.now().isoformat()
    }


def create_full_processing_pipeline(
    vendor_files: List[Dict[str, Any]],
    include_code_comparison: bool = True,
    pipeline_context: Optional[Dict[str, Any]] = None
) -> str:
    """
    創建完整的處理管道：廠商檔案處理 + 代碼比較
    使用正確的 Dramatiq 管道語法
    
    Args:
        vendor_files: 廠商檔案列表
        include_code_comparison: 是否包含代碼比較步驟
        pipeline_context: 管道上下文
        
    Returns:
        str: 管道ID
    """
    from src.tasks.pipeline_utils import get_pipeline_manager, PipelineStatus
    from dramatiq import pipeline
    
    # 導入任務
    try:
        import sys
        sys.path.append('.')
        from dramatiq_tasks import run_code_comparison_task
        logger.info("✅ 成功導入 run_code_comparison_task")
    except ImportError as e:
        logger.warning(f"無法導入 run_code_comparison_task: {e}，將跳過代碼比較步驟")
        include_code_comparison = False
    
    # 使用管道管理器創建上下文
    manager = get_pipeline_manager()
    pipeline_id = manager.create_pipeline_context(
        pipeline_type="full_processing_pipeline",
        metadata={
            'vendor_files_count': len(vendor_files),
            'include_code_comparison': include_code_comparison,
            'vendor_codes': list(set(vf.get('vendor_code') for vf in vendor_files if vf.get('vendor_code'))),
            'created_by': 'pipeline_tasks',
            **(pipeline_context or {})
        }
    )
    
    # 計算總任務數
    total_tasks = len(vendor_files)
    if include_code_comparison:
        total_tasks += len(vendor_files)  # 每個廠商檔案對應一個代碼比較任務
    
    # 更新任務計數
    manager.update_pipeline_context(
        pipeline_id,
        status=PipelineStatus.RUNNING,
        task_count=total_tasks
    )
    
    logger.info(f"[PIPELINE] 創建完整處理管道: pipeline_id={pipeline_id}")
    logger.info(f"   廠商檔案: {len(vendor_files)} 個")
    logger.info(f"   包含代碼比較: {include_code_comparison}")
    logger.info(f"   總任務數: {total_tasks}")
    
    try:
        # 為每個廠商檔案創建管道：vendor_processing -> code_comparison
        for i, vendor_file in enumerate(vendor_files):
            file_pipeline_context = {
                'pipeline_id': pipeline_id,
                'task_index': i,
                'total_tasks': total_tasks,
                'file_index': i,
                **(pipeline_context or {})
            }
            
            # 第一步：處理廠商檔案
            vendor_task_message = process_vendor_files_task.message(
                vendor_code=vendor_file.get('vendor_code'),
                mo=vendor_file.get('mo'),
                temp_path=vendor_file.get('temp_path'),
                pd=vendor_file.get('pd', 'default'),
                lot=vendor_file.get('lot', 'default'),
                email_subject=vendor_file.get('email_subject', ''),
                email_body=vendor_file.get('email_body', ''),
                pipeline_context=file_pipeline_context
            )
            
            if include_code_comparison:
                # 使用正確的 Dramatiq 管道語法
                code_comparison_message = run_code_comparison_task.message()
                
                # 創建管道：vendor_processing -> code_comparison
                # 使用 pipeline([task1.message(...), task2.message()]).run() 語法
                pipe = pipeline([
                    vendor_task_message,
                    code_comparison_message  # 空參數，自動接收上一個任務結果
                ])
                
                # 運行管道
                pipe.run()
                
                logger.debug(f"✅ 已創建完整管道 (檔案 {i+1}/{len(vendor_files)}): {vendor_file.get('vendor_code')}")
            else:
                # 只運行廠商檔案處理
                vendor_task_message.send()
                
                logger.debug(f"✅ 已創建廠商處理任務 (檔案 {i+1}/{len(vendor_files)}): {vendor_file.get('vendor_code')}")
        
        logger.info(f"[PIPELINE] 完整處理管道已啟動: pipeline_id={pipeline_id}")
        return pipeline_id
        
    except Exception as e:
        logger.error(f"[PIPELINE] 創建完整處理管道失敗: {str(e)}")
        import traceback
        logger.error(f"詳細錯誤: {traceback.format_exc()}")
        
        # 更新管道狀態為失敗
        manager.update_pipeline_context(
            pipeline_id,
            status=PipelineStatus.FAILED,
            metadata_update={'error': str(e), 'failed_at': datetime.now().isoformat()}
        )
        
        raise


# 導出所有管道任務和函數
__all__ = [
    'process_vendor_files_task',
    'pipeline_completion_task',
    'create_vendor_processing_pipeline',
    'create_sequential_vendor_pipeline',
    'create_full_processing_pipeline',
    'get_pipeline_status',
    'cancel_pipeline'
]